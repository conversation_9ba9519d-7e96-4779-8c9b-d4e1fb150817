[tool.poetry]
name = "sagecloud-core"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
autopep8 = "^2.1.0"
boto3 = "^1.34.108"
daphne = "^4.1.2"
django-debug-toolbar = "^4.3.0"
drf-spectacular = "^0.27.2"
djangorestframework-simplejwt = "5.4.0"
markupsafe = "^2.1.5"
markdown = "^3.6"
djangorestframework = "3.15.2"
django-import-export = "^4.0.3"
pylint = "^3.2.0"
locust = "^2.27.0"
flower = "^2.0.1"
django-redis = "^5.4.0"
redis = "^5.0.4"
celery = "^5.4.0"
pytest-django = "^4.8.0"
channels-redis = "^4.2.0"
channels = "^4.1.0"
whitenoise = "^6.6.0"
sendgrid = "^6.11.0"
python-decouple = "^3.8"
python-dateutil = "^2.9.0.post0"
pillow = "^10.3.0"
psycopg2-binary = "^2.9.9"
docutils = "^0.21.2"
django-storages = "^1.14.3"
django-filter = "^24.2"
django-cors-headers = "^4.3.1"
django-widget-tweaks = "^1.5.0"
django = "5.1.5"
dj-database-url = "^2.1.0"
botocore = "^1.34.108"
cuid2 = "^2.0.1"
django-jazzmin = "^3.0.0"
django-extensions = "^3.2.3"
email-validator = "^2.1.1"
psutil = "^5.9.8"
logtail-python = "^0.2.10"
hvac = "^2.3.0"
django-celery-beat = "^2.7.0"
drf-standardized-errors = "^0.14.1"
djangorestframework-api-key = "^3.0.0"
django-ipware = "^7.0.1"
opensearch-py = "^2.8.0"
colorlog = "^6.9.0"
sentry-sdk = "^2.22.0"
pykolofinance = "^3.0.3"
drf-extra-fields = "^3.7.0"
gunicorn = "^23.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
