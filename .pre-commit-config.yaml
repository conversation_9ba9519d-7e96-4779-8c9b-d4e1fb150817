repos:
  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.7.2
    hooks:
      - id: uv-lock

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-added-large-files


  - repo: https://github.com/PyCQA/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]


  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black


  - repo: https://github.com/pycqa/flake8
    rev: 7.2.0
    hooks:
      - id: flake8

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: 'v0.11.2'
    hooks:
      # Run the linter.
      - id: ruff
        args: [ --fix ]
      #Run the formatter.
      # - id: ruff-format
