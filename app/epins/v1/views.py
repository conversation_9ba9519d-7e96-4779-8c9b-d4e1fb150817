from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON>ey
from rest_framework.response import Response
from common.dtos import ServiceResponseData
from epins.v1.serializer import VendEpinSerializer


@extend_schema_view(
    vend=header,
)
class EpinsViewSet(viewsets.GenericViewSet):
    http_method_names = ['post']
    permission_classes = [HasAPIKey]

    @action(
        methods=['POST'],
        serializer_class=VendEpinSerializer,
        detail=False,
        url_path="vend"
    )
    def vend(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)