from rest_framework import serializers

from common.enums import ProviderEnum, ProductEnum
from epins.dtos import VendEpinParams
from epins.enums import EpinNetwork, Amount
from epins.service_router import EpinServiceRouter
from provider.utils import get_vender


class VendEpinSerializer(serializers.Serializer):
    reference = serializers.CharField()
    network = serializers.ChoiceField(choices=EpinNetwork.choices())
    amount = serializers.ChoiceField(choices=Amount.choices())
    quantity = serializers.IntegerField()

    def validate(self, attrs):
        data = super().validate(attrs)
        data['network'] = EpinNetwork(data['network'])
        if data['quantity'] <= 0:
            raise serializers.ValidationError({'quantity': 'Quantity must be greater than zero.'})
        return data

    def save(self):
        params = VendEpinParams(**self.validated_data)
        vender = get_vender(ProductEnum.Epin, ProviderEnum(params.network.value.title()))
        response = EpinServiceRouter().resolve(vender).vend(params)

        return response

