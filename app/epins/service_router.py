from typing import Optional

from common.enums import VenderEnum
from epins.contracts import EpinServiceInterface
from epins.services.mama_africa import MamaAfricaEpinService


class EpinServiceRouter:
    """
    Routes epin service requests to the appropriate vendor-specific implementation.

    This class acts as a dynamic service router that delegates epin-related method calls
    (e.g., pin generation) to the correct service implementation based on
    the provided `driver` (vendor). This abstraction allows the business logic to interact
    with a unified interface while enabling easy switching between different epin service providers.
    Currently defaults to MamaAfrica service for all requests.
    """

    @staticmethod
    def resolve(driver: VenderEnum) -> EpinServiceInterface:
        """
        Returns the appropriate epin service based on the driver.

        Returns:
            EpinServiceInterface: The epin service instance.
        """
        return MamaAfricaEpinService()