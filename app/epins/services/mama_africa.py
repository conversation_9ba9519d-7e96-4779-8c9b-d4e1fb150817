import logging
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from epins.contracts import EpinServiceInterface
from epins.dtos import VendEpinParams
from integration.mama_africa.client import MamaAfricaClient
from integration.mama_africa.dtos import RedeemBulkEpinPayload

logger = logging.getLogger(__name__)

class MamaAfricaEpinService(EpinServiceInterface):

    def __init__(self):
        self.client = MamaAfricaClient()

    def vend(self, params: VendEpinParams) -> ServiceResponseData:
        try:
            payload = self.__resolve_vend_epin_payload(params)
            status_code, response = self.client.redeem_bulk_epin(payload)
            
            if response.success:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.error.message or "Unable to redeem epins",
            )
        except Exception as e:
            logger.exception(f"E-PIN vending failed for reference {params.reference}: {str(e)}")
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )
            
    def check_available_epins(self) -> ServiceResponseData:
        try:
            status_code, response = self.client.fetch_available_epins_count()
            
            if status_code == 200 and response.success:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message="Available E-PINs fetched successfully",
                    data=response.to_dict(),
                )
                
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message or "Failed to fetch available E-PINs",
                data=response.to_dict(),
            )
        except Exception as e:
            logger.exception(f"Failed to fetch available E-PINs: {str(e)}")
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )
    
    @staticmethod
    def __resolve_vend_epin_payload(params: VendEpinParams) -> RedeemBulkEpinPayload:
        return RedeemBulkEpinPayload(
            network=params.network.value,
            amount=int(params.amount),
            reference=params.reference,
            quantity=params.quantity
        )