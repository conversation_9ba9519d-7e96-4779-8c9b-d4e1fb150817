import logging

from betting.contracts import BettingServiceInterface
from betting.dtos import FundBetParams, ValidateBetIdParams
from betting.enums import BetBiller
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.shago.client import ShagoClient
from integration.shago.dto import BetAccountPaymentPayload, BetAccountValidationPayload

logger = logging.getLogger(__name__)


class ShagoBettingService(BettingServiceInterface):

    def __init__(self):
        self.client = ShagoClient()

    def fund(self, params: FundBetParams) -> ServiceResponseData:
        try:
            payload = self.__resolve_fund_bet_payload(params)
            _, response = self.client.bet_account_payment(payload)
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            if response.status == "400":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message,
                data=response.to_dict(),
            )

        except Exception as e:
            logger.exception(
                f"Betting Funding failed for reference {params.reference}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Pending.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )

    def __resolve_fund_bet_payload(params: FundBetParams) -> BetAccountPaymentPayload:
        return BetAccountPaymentPayload(
            name=params.biller_code,
            customerId=params.bet_id,
            amount=params.amount,
            request_id=params.reference,
        )

    def validate(self, params: ValidateBetIdParams) -> ServiceResponseData:
        try:
            payload = self.__resolve_bet_account_validation_payload(params)
            _, response = self.client.bet_account_validation(payload)
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            if response.status == "400":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message,
                data=response.to_dict(),
            )

        except Exception as e:
            logger.exception(
                f"Betting Account Validation failed for Customer ID {params.bet_id}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )

    def __resolve_bet_account_validation_payload(
        params: ValidateBetIdParams,
    ) -> BetAccountValidationPayload:
        return BetAccountValidationPayload(
            type=params.biller_code,
            customerId=params.bet_id,
        )

    def fetch_billers(self) -> ServiceResponseData:
        return ServiceResponseData(
            status=ServiceResponseStatus.Success.value,
            message="Billers fetched successfully.",
            data=BetBiller.values(),
        )
