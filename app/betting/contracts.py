from abc import ABC, abstractmethod

from betting.dtos import FundBetParams, ValidateBetIdParams
from common.dtos import ServiceResponseData


class BettingServiceInterface(ABC):
    @abstractmethod
    def fund(self, params: FundBetParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def validate(self, params: ValidateBetIdParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def fetch_billers(self) -> ServiceResponseData:
        pass
