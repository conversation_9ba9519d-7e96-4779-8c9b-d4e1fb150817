from dataclasses import asdict, dataclass, field
from typing import Any, Dict, Optional

from core import settings
from integration.nibbs.easypay.helpers import get_transaction_id
from integration.utils import FieldMapper


class ToDict:
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


"""
Name Enquiry Payload/Response
"""


@dataclass
class NameEnquiryPayload(ToDict):
    accountNumber: str
    destinationInstitutionCode: str
    channelCode: str = 2
    transactionId: str = field(default_factory=get_transaction_id)


@dataclass(init=False)
class NameEnquiryResponse(ToDict, FieldMapper):
    responseCode: Optional[str] = "XX"
    sessionID: Optional[str] = None
    transactionId: Optional[str] = None
    channelCode: Optional[str] = None
    destinationInstitutionCode: Optional[str] = None
    accountNumber: Optional[str] = None
    accountName: Optional[str] = None
    bankVerificationNumber: Optional[str] = None
    kycLevel: Optional[str] = None


"""
Funds transfer Payload/Response
"""


@dataclass
class FundsTransferPayload(ToDict):
    sourceInstitutionCode: str
    amount: str
    beneficiaryAccountName: str
    beneficiaryAccountNumber: str
    beneficiaryBankVerificationNumber: str
    beneficiaryKYCLevel: str
    originatorAccountName: str
    originatorAccountNumber: str
    originatorBankVerificationNumber: str
    originatorKYCLevel: str
    destinationInstitutionCode: str
    mandateReferenceNumber: str
    nameEnquiryRef: str
    originatorNarration: str
    paymentReference: str
    beneficiaryNarration: str
    transactionId: str = field(default_factory=get_transaction_id)
    billerId: str = field(default_factory=lambda: settings.EASYPAY_BILLER_ID)
    channelCode: int = 2


@dataclass(init=False)
class FundsTransferResponse(ToDict, FieldMapper):
    responseCode: Optional[str] = "XX"
    sessionID: Optional[str] = None
    transactionId: Optional[str] = None
    channelCode: Optional[str] = None
    nameEnquiryRef: Optional[str] = None
    destinationInstitutionCode: Optional[str] = None
    beneficiaryAccountName: Optional[str] = None
    beneficiaryAccountNumber: Optional[str] = None
    beneficiaryKYCLevel: Optional[str] = None
    beneficiaryBankVerificationNumber: Optional[str] = None
    originatorAccountName: Optional[str] = None
    originatorAccountNumber: Optional[str] = None
    originatorBankVerificationNumber: Optional[str] = None
    originatorKYCLevel: Optional[str] = None
    transactionLocation: Optional[str] = None
    narration: Optional[str] = None
    paymentReference: Optional[str] = None
    amount: Optional[str] = None


"""
Transaction Query Response
"""


@dataclass(init=False)
class TransactionQueryResponse(ToDict, FieldMapper):
    responseCode: Optional[str] = "XX"
    sessionID: Optional[str] = None
    transactionId: Optional[str] = None
    channelCode: Optional[str] = None
    sourceInstitutionCode: Optional[str] = None
