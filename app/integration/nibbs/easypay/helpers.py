import random
from datetime import datetime

import requests
from core import settings


def get_transaction_id():
    client_code = settings.EASYPAY_CLIENT_CODE
    timestamp = datetime.now().strftime("%y%m%d%H%M%S")
    rand12 = f"{random.randint(121212, 989898)}{random.randint(121212, 989898)}"
    return f"{client_code}{timestamp}{rand12}"


def generate_secret_key():
    response = requests.get(
        f"{settings.EASYPAY_BASE_URL}/ClientSecretGenerator/"
        + settings.EASYPAY_CLIENT_ID
        + "/"
        + settings.EASYPAY_APP_NAME
    )
    return response
