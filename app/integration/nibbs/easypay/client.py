from datetime import timedelta

import requests
from core import settings
from django.core.cache import cache
from django.utils import timezone
from integration.base import BaseClient
from integration.nibbs.easypay.dtos import (
    FundsTransferPayload,
    FundsTransferResponse,
    NameEnquiryPayload,
    NameEnquiryResponse,
    TransactionQueryResponse,
)


class EasypayClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "Easypay"
    CACHE_NAME = "nibbs-token-key"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.EASYPAY_BASE_URL
        self.client_id = settings.EASYPAY_CLIENT_ID
        self.access_token = self.__get_access_token()
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": "Bearer " + self.access_token,
        }

    def __get_access_token(self):
        access_token = cache.get(self.CACHE_NAME)
        if access_token:
            return access_token

        response = requests.post(
            self.base_url + "/reset",
            data={
                "client_id": self.client_id,
                "scope": self.client_id + "/.default",
                "client_secret": settings.EASYPAY_CLIENT_SECRET,
                "grant_type": "client_credentials",
            },
        )

        if response.ok:
            auth_data = response
            access_token = auth_data["access_token"]
            expires_in = int(auth_data["expires_in"])

            cache_data = {
                "access_token": access_token,
                "expires_at": timezone.now() + timedelta(seconds=expires_in),
            }
            cache.set(self.CACHE_NAME, cache_data, timeout=expires_in - 60)

            return access_token

        raise Exception("Failed to get access token")

    def name_enquiry(self, payload: NameEnquiryPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/nipservice/v1/nip/nameenquiry",
            data=data,
        )

        if not response:
            return status_code or 400, NameEnquiryResponse()

        return status_code, NameEnquiryResponse(**response)

    def funds_transfer(self, payload: FundsTransferPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/nipservice/v1/nip/fundstransfer",
            data=data,
        )

        if not response:
            return status_code or 400, FundsTransferResponse()

        return status_code, FundsTransferResponse(**response)

    def transaction_query(self, transaction_id: str):
        data = {"transactionId": transaction_id}
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/nipservice/v1/nip/tsq", data=data
        )

        if not response:
            return status_code or 400, TransactionQueryResponse()

        return status_code, TransactionQueryResponse(**response)
