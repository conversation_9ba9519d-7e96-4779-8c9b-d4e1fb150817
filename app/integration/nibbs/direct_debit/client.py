# integration/nibss/client.py
from typing import Union

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from integration.base import BaseClient
from integration.nibbs.direct_debit.dto import (
    CreateMandateErrorResponse,
    CreateMandateSuccessResponse,
    GenerateAccessTokenData,
    RetrieveMandateStatusErrorResponse,
    RetrieveMandateStatusSuccessResponse,
    UpdateMandateResponse,
)


class NibssMandateManagementClient(BaseClient):
    cached_key = "nibbs-mandate-management-key"

    def __init__(self):
        super().__init__()
        self.ClientName = "NibssMandateClient"
        self.base_url = settings.NIBSS_BASE_URL
        self.client_id = settings.NIBSS_CLIENT_ID
        self.client_secret = settings.NIBSS_CLIENT_SECRET
        self.reset_api_key = settings.NIBSS_RESET_API_KEY

        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_access_token()}",
        }

    def _cache_get(self, key):
        return cache.get(key)

    def _cache_set(self, key, value, timeout=300):
        cache.set(key, value, timeout)

    def _is_production_env(self):
        return getattr(settings, "ENVIRONMENT_INSTANCE", "").lower() == "production"

    def _get_access_token(self) -> str:
        cached = self._cache_get(self.cached_key)
        if not cached or timezone.now() >= cached["expiresAt"]:
            token_data = self._generate_access_token()
            self._save_token(token_data)
            return token_data.access_token
        return cached["accessToken"]

    def _generate_access_token(self) -> GenerateAccessTokenData:
        form_data = {
            "client_id": self.client_id,
            "scope": f"{self.client_id}/.default",
            "client_secret": self.client_secret,
            "grant_type": "client_credentials",
        }
        response, _ = self._make_request(
            "POST", f"{self.base_url}/v2/reset", data=form_data
        )
        return GenerateAccessTokenData.from_dict(response)

    def _save_token(self, data: GenerateAccessTokenData):
        expires_at = timezone.now() + timezone.timedelta(seconds=data.expires_in)
        self._cache_set(
            self.cached_key,
            {"accessToken": data.access_token, "expiresAt": expires_at},
            timeout=data.expires_in - 60,
        )

    def _auth_payload(self):
        return settings.NIBSS_PROD if self._is_production_env() else settings.NIBSS_TEST

    def create_mandate(
        self, data: dict
    ) -> Union[CreateMandateSuccessResponse, CreateMandateErrorResponse]:
        payload = {"auth": self._auth_payload(), "mandateRequests": [data]}
        response, status = self._make_request(
            "POST", f"{self.base_url}/mandate/v1/e-mandate-account", payload
        )
        result = response[0] if isinstance(response, list) else response
        if status != "200":
            return CreateMandateErrorResponse.from_dict(result)
        return CreateMandateSuccessResponse.from_dict(result)

    def retrieve_mandate_status(
        self, mandate_code: str
    ) -> Union[
        RetrieveMandateStatusSuccessResponse, RetrieveMandateStatusErrorResponse
    ]:
        payload = {"auth": self._auth_payload(), "mandateCodes": [mandate_code]}
        response, status = self._make_request(
            "POST", f"{self.base_url}/mandate/v1/status", payload
        )
        result = response[0] if isinstance(response, list) else response
        if status != "200":
            return RetrieveMandateStatusErrorResponse.from_dict(result)
        return RetrieveMandateStatusSuccessResponse.from_dict(result)

    def update_mandate(self, data: dict) -> UpdateMandateResponse:
        payload = {
            "auth": self._auth_payload(),
            "mandateUpdateRequests": [
                {
                    "mandateCode": data["mandate_code"],
                    "phoneNumber": data["phone_number"],
                    "payerName": data["payer_name"],
                    "payerAddress": data["payer_address"],
                    "status": 1,
                    "workFlowStatus": 1,
                    "emailAddress": data["email"],
                }
            ],
        }
        response, _ = self._make_request(
            "POST", f"{self.base_url}/mandate/v1/biller/update", payload
        )
        result = response[0] if isinstance(response, list) else response
        return UpdateMandateResponse.from_dict(result)
