from dataclasses import dataclass
from typing import List, Optional


@dataclass
class CreateMandateErrorResponse:
    status: str
    errorMessageList: List[str]
    data: Optional[str]

    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)


@dataclass
class CreateMandateSuccessResponse:
    responseCode: str
    mandateCode: Optional[str]
    subscriberCode: Optional[str]
    phoneNumber: Optional[str]
    responseDescription: Optional[str]

    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)


@dataclass
class RetrieveMandateStatusSuccessResponse:
    accountName: Optional[str]
    accountNumber: Optional[str]
    bvn: Optional[str]
    email: Optional[str]
    narration: Optional[str]
    payerAddress: Optional[str]
    payer: Optional[str]
    phoneNumber: Optional[str]
    amount: Optional[str]
    subscriberCode: Optional[str]
    frequency: Optional[str]
    kyc: Optional[str]
    bankCode: Optional[str]
    bank: Optional[str]
    status: Optional[str]
    endDate: Optional[str]
    startDate: Optional[str]
    productId: Optional[str]
    billerId: Optional[str]
    biller: Optional[str]
    rc: Optional[str]
    billerAccountName: Optional[str]
    billerAccountNumber: Optional[str]
    billerBankCode: Optional[str]
    billerBankName: Optional[str]
    workFlowStatusDescription: Optional[str]
    responseCode: Optional[str]
    workFlowStatus: Optional[str]

    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)


@dataclass
class RetrieveMandateStatusErrorResponse:
    responseCode: Optional[str]


class UpdateMandateResponse:
    mandateCode: str
    responseCode: str
    responseDescription: str

    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)


class GenerateAccessTokenData:
    expires_in: int
    ext_expires_in: int
    access_token: str
    token_type: str

    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)
