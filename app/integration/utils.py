from dataclasses import asdict, fields, is_dataclass
from typing import Any, Dict, get_origin, Union, get_args, List


class ToDict:
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class FieldMapper:
    def __init__(self, **kwargs):
        allowed_fields = {f.name: f for f in fields(self)}

        for key, value in kwargs.items():
            if key in allowed_fields:
                field_info = allowed_fields[key]
                converted_value = self._convert_value(value, field_info.type)
                setattr(self, key, converted_value)

    def _convert_value(self, value, field_type):
        """Convert value to the appropriate type based on field annotation."""
        if value is None:
            return None

        # Handle Optional types (Union[X, None])
        if get_origin(field_type) is Union:
            args = get_args(field_type)
            if len(args) == 2 and type(None) in args:
                # This is Optional[X], get the non-None type
                non_none_type = args[0] if args[1] is type(None) else args[1]
                return self._convert_value(value, non_none_type)

        # Handle List types
        if get_origin(field_type) is list or get_origin(field_type) is List:
            if isinstance(value, list):
                list_item_type = get_args(field_type)[0] if get_args(field_type) else Any
                return [self._convert_value(item, list_item_type) for item in value]
            return value

        # Handle dataclass conversion
        if is_dataclass(field_type) and isinstance(value, dict):
            return field_type(**value)

        # For primitive types or already correct types
        return value

    @classmethod
    def from_dict(cls, data: dict):
        """Create an instance from a dictionary."""
        return cls(**data)
