from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional


class ToDict:
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


"""
Vend Vtu Payload
"""


@dataclass
class VendVtuPayload(ToDict):
    product_code: str
    amount: float
    phone_number: str
    marchant_ref: str


"""
Vend Data Payload
"""


@dataclass
class VendDataPayload(ToDict):
    data_code: str
    product_code: str
    phone_number: str
    marchant_ref: str


@dataclass
class SoniteResponse(ToDict):
    responseCode: Optional[str] = "09"
    responseMessage: Optional[str] = "Unable to complete request"
    responseData: Optional[List[Any]] = None

    def __init__(self, **kwargs):
        allowed_keys = {"responseCode", "responseMessage", "responseData"}
        self.__dict__.update({k: v for k, v in kwargs.items() if k in allowed_keys})
