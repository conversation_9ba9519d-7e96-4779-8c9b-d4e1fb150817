import base64
import hashlib
from datetime import datetime

from core import settings
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from integration.base import BaseClient
from integration.sonite.dto import SoniteResponse, VendDataPayload, VendVtuPayload
from pytz import timezone


class SoniteClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "Sonite"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.SONITE_BASE_URL
        self.api_secret = settings.SONITE_API_SECRET
        self.api_token = settings.SONITE_API_TOKEN
        self.merchant_code = settings.SONITE_MERCHANT_CODE
        self.authorization_code = self.__generate_authorization_code()
        print(self.authorization_code)
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-api-key": self.api_secret,
            "Authorization": "Bearer " + self.authorization_code,
        }

    def __generate_authorization_code(self) -> str:
        timestamp = datetime.now(timezone("Africa/Lagos"))
        timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")

        hashed_key = hashlib.sha256(self.api_secret.encode("utf-8")).digest()

        merchant_code_plus_timestamp = f"{self.merchant_code}|{timestamp_str}"
        encoded_data = merchant_code_plus_timestamp.encode("utf-8")

        cipher = Cipher(algorithms.AES(hashed_key), modes.ECB())
        encryptor = cipher.encryptor()

        pad_len = 16 - (len(encoded_data) % 16)
        padded_data = encoded_data + bytes([pad_len] * pad_len)

        encrypted = encryptor.update(padded_data) + encryptor.finalize()

        encoded_result = base64.b64encode(encrypted).decode()

        return f"{self.api_token}.{encoded_result}"

    def vend_vtu(self, payload: VendVtuPayload) -> tuple[int, SoniteResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/product/vendVtu", data=data
        )

        if not response:
            return status_code or 400, SoniteResponse()

        return status_code, SoniteResponse(**response)

    def vend_data(self, payload: VendDataPayload) -> tuple[int, SoniteResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/product/vendData", data=data
        )

        if not response:
            return status_code or 400, SoniteResponse()

        return status_code, SoniteResponse(**response)

    def data_bundles(self, product_code: str) -> tuple[int, SoniteResponse]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/product/getDataBundle",
            data={"product_code": product_code},
        )

        if not response:
            return status_code or 400, SoniteResponse()

        return status_code, SoniteResponse(**response)

    def products(self) -> tuple[int, SoniteResponse]:
        response, status_code = self._make_request(
            method="GET", url=f"{self.base_url}/product/getProducts", data=None
        )

        if not response:
            return status_code or 400, SoniteResponse()

        return status_code, SoniteResponse(**response)

    def product_categories(self) -> tuple[int, SoniteResponse]:
        response, status_code = self._make_request(
            method="GET", url=f"{self.base_url}/product/getProductCategories", data=None
        )

        if not response:
            return status_code or 400, SoniteResponse()

        return status_code, SoniteResponse(**response)
