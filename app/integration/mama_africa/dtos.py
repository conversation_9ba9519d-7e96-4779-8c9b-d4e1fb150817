from dataclasses import dataclass, field
from typing import Any, List, Optional
from epins.dtos import EpinDetails
from integration.utils import FieldMapper, ToDict

"""
Error response
"""

@dataclass(init=False)
class ErrorDetail(ToDict, FieldMapper):
    message: Optional[str] = None
    error_type: Optional[str] = None

@dataclass(init=False)
class ErrorResponse(ToDict, FieldMapper):
    success: Optional[bool] = False
    error: Optional[ErrorDetail] = None
    detail: Optional[str] = None


"""
Get Vendors Response
"""


@dataclass(init=False)
class GetVendorsResponse(ToDict, FieldMapper):
    VendorList: List[Any]


"""
Voucher request Payload/Response
"""


@dataclass
class VoucherRequestPayload(ToDict):
    reference: str
    product_id: int
    supplier_id: int
    demom_code: str


@dataclass(init=False)
class VoucherRequestResponse(ToDict, FieldMapper):
    Respcode: Optional[int]
    ResponseMessage: Optional[str]
    vouchers: Optional[List[Any]]


"""
Generate bulk epin Payload/Response
"""


@dataclass
class GenerateBulkEpinPayload(ToDict):
    reference: str
    reference: str
    value: int
    value: int


@dataclass(init=False)
class GenerateBulkEpinResponse(ErrorResponse, ToDict, FieldMapper):
    success: Optional[bool] = False
    message: Optional[str] = "Unable to generate bulk epin"


"""
Redeem bulk epin Payload/Response
"""


@dataclass
class RedeemBulkEpinPayload(ToDict):
    network: str
    amount: int
    reference: str
    quantity: int


@dataclass(init=False)
class RedeemBulkEpinResultsData(ToDict, FieldMapper):
    network: Optional[str] = None
    amount: Optional[int] = None
    description: Optional[str] = None
    pin_number: Optional[str] = None
    serial_number: Optional[str] = None
    unique_id: Optional[str] = None
    sequence_number: Optional[str] = None
    confirmation_code: Optional[str] = None
    transaction_id: Optional[str] = None


@dataclass(init=False)
class RedeemBulkEpinResponse(ErrorResponse, ToDict, FieldMapper):
    success: Optional[bool] = False
    message: Optional[str] = None
    results: List[RedeemBulkEpinResultsData] = field(default_factory=list)

    def to_dict(self):
        results = []
        for r in self.results:
            results.append(EpinDetails(
                Amount=r.amount,
                pin=r.pin_number,
                serial_number=r.serial_number,
                instruction=r.description,
            ).to_dict())

        return results



"""
Fetch Redeemed epins request logs query params/ Response
"""


@dataclass
class FetchRedeemedEpinsRequestLogsQueryParams(ToDict):
    start: Optional[str] = None
    search: Optional[str] = None
    end: Optional[str] = None
    limit: Optional[str] = None
    offset: Optional[str] = None


@dataclass(init=False)
class FetchRedeemedEpinsRequestLogsResultData(ToDict, FieldMapper):
    redeemed_epin_request_log_created_at: Optional[str] = None
    network: Optional[str] = None
    amount: Optional[int] = None
    unique_id: Optional[str] = None
    redeemed_epin_request_log_reference: Optional[str] = None


@dataclass(init=False)
class FetchRedeemedEpinsRequestLogsResponse(ErrorResponse, ToDict, FieldMapper):
    count: Optional[int] = None
    next: Optional[str] = None
    previous: Optional[str] = None
    results: List[FetchRedeemedEpinsRequestLogsResultData] = field(default_factory=list)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        results_data = kwargs.get("results", [])
        if results_data and isinstance(results_data[0], dict):
            self.results = [
                FetchRedeemedEpinsRequestLogsResultData(
                    **{
                        "redeemed_epin_request_log_created_at": r.get(
                            "redeemed_epin_request_log_created_at"
                        ),
                        "network": r.get("network"),
                        "amount": r.get("amount"),
                        "unique_id": r.get("unique_id"),
                        "redeemed_epin_request_log_reference": r.get(
                            "redeemed_epin_request_log_reference"
                        ),
                    }
                )
                for r in results_data
            ]


"""
Fetch bulk epins request logs query params/ Response
"""


@dataclass
class FetchBulkEpinsRequestLogsQueryParams(ToDict):
    start: Optional[str] = None
    search: Optional[str] = None
    end: Optional[str] = None
    limit: Optional[str] = None
    offset: Optional[str] = None


@dataclass(init=False)
class FetchBulkEpinsRequestLogsResultData(ToDict, FieldMapper):
    id: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    reference: Optional[str] = None
    network: Optional[str] = None
    value: Optional[int] = None
    requested_quantity: Optional[int] = None
    generated_quantity: Optional[int] = None
    last_error_message: Optional[str] = None


@dataclass(init=False)
class FetchBulkEpinsRequestLogsResponse(ErrorResponse, ToDict, FieldMapper):
    count: Optional[int] = None
    next: Optional[str] = None
    previous: Optional[str] = None
    results: List[FetchBulkEpinsRequestLogsResultData] = field(default_factory=list)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        results_data = kwargs.get("results", [])
        if results_data and isinstance(results_data[0], dict):
            self.results = [
                FetchBulkEpinsRequestLogsResultData(
                    **{
                        "id": r.get("id"),
                        "created_at": r.get("created_at"),
                        "updated_at": r.get("updated_at"),
                        "reference": r.get("reference"),
                        "network": r.get("network"),
                        "value": r.get("value"),
                        "requested_quantity": r.get("requested_quantity"),
                        "generated_quantity": r.get("generated_quantity"),
                        "last_error_message": r.get("last_error_message"),
                    }
                )
                for r in results_data
            ]


"""
Fetch available epin counts Response
"""


@dataclass(init=False)
class FetchAvailableEpinCountsResultData(ToDict, FieldMapper):
    network: Optional[str] = None
    value: Optional[int] = None
    available_count: Optional[int] = None


@dataclass(init=False)
class FetchAvailableEpinCountsResponse(ErrorResponse, ToDict, FieldMapper):
    success: Optional[bool] = False
    message: Optional[str] = None
    results: List[FetchAvailableEpinCountsResultData] = field(default_factory=list)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        results_data = kwargs.get("results", [])
        if results_data and isinstance(results_data[0], dict):
            self.results = [
                FetchAvailableEpinCountsResultData(
                    **{
                        "network": r.get("network"),
                        "value": r.get("value"),
                        "available_count": r.get("available_count"),
                    }
                )
                for r in results_data
            ]
