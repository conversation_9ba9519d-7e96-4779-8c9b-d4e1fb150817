from typing import Union

from core import settings
from integration.base import BaseClient
from integration.mama_africa.dtos import (
    ErrorResponse,
    FetchAvailableEpinCountsResponse,
    FetchBulkEpinsRequestLogsResponse,
    FetchRedeemedEpinsRequestLogsQueryParams,
    FetchRedeemedEpinsRequestLogsResponse,
    GenerateBulkEpinPayload,
    GenerateBulkEpinResponse,
    GetVendorsResponse,
    RedeemBulkEpinPayload,
    RedeemBulkEpinResponse,
    VoucherRequestPayload,
    VoucherRequestResponse,
)


class MamaAfricaClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "JAMB"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.MAMA_AFRICA_BASE_URL
        self.xApiKey = settings.MAMA_AFRICA_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-api-key": self.xApiKey,
        }

    def get_vendors(
        self, reference: str
    ) -> [int, Union[ErrorResponse, GetVendorsResponse]]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v1/mama-africa/vendors/",
            data={"reference": reference},
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, GetVendorsResponse(**response)

    def voucher_request(
        self, payload: VoucherRequestPayload
    ) -> [int, Union[ErrorResponse, VoucherRequestResponse]]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v1/mama-africa/voucher-request/",
            data=data,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, VoucherRequestResponse(**response)

    def generate_bulk_epin(
        self, payload: GenerateBulkEpinPayload
    ) -> [int, Union[ErrorResponse, GenerateBulkEpinResponse]]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v1/mama-africa/bulk/generate-epin/",
            data=data,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, GenerateBulkEpinResponse(**response)

    def redeem_bulk_epin(
        self, payload: RedeemBulkEpinPayload
    ) -> tuple[int, Union[ErrorResponse, RedeemBulkEpinResponse]]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v1/mama-africa/bulk/redeem-epin/",
            data=data,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, RedeemBulkEpinResponse(**response)

    def fetch_redeemed_epin_request_logs(
        self, query_params: FetchRedeemedEpinsRequestLogsQueryParams
    ) -> [int, Union[ErrorResponse, FetchRedeemedEpinsRequestLogsResponse]]:
        data = query_params.to_dict()
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v1/mama-africa/redeemed_epin_request_logs",
            data=data,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, FetchRedeemedEpinsRequestLogsResponse(**response)

    def fetch_bulk_epin_request_logs(
        self, query_params: FetchRedeemedEpinsRequestLogsQueryParams
    ) -> [int, Union[ErrorResponse, FetchBulkEpinsRequestLogsResponse]]:
        data = query_params.to_dict()
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v1/mama-africa/bulk_epin_request_logs",
            data=data,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, FetchBulkEpinsRequestLogsResponse(**response)

    def fetch_available_epins_count(
        self,
    ) -> [int, Union[ErrorResponse, FetchAvailableEpinCountsResponse]]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v1/mama-africa/bulk/available_epin_counts",
            data=None,
        )

        if not response:
            return status_code or 400, ErrorResponse(**response)

        return status_code, FetchAvailableEpinCountsResponse(**response)
