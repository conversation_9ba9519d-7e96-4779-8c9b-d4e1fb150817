from dataclasses import dataclass
from typing import Dict, List, Optional, Any

from cable_tv.dtos import ValidatedCableDetails
from integration.utils import FieldMapper, ToDict


# Validate meter Payload/Response
@dataclass
class ValidateMeterPayload(ToDict):
    disco: str
    meterNo: str
    type: str
    serviceCode: str = "AOV"

@dataclass(init=False)
class ElectricityValidationResponse(ToDict, FieldMapper):
    meterNo: Optional[str] = None
    disco: Optional[str] = None
    status: Optional[str] = "failed"  # Default to failed
    accountNo: Optional[str] = None
    customerName: Optional[str] = None
    customerAddress: Optional[str] = None
    customerDistrict: Optional[str] = None
    phoneNumber: Optional[str] = None
    minimumAmount: Optional[float] = None
    type: Optional[str] = None


#Electricity purchase Payload/Response
@dataclass
class ElectricityPurchasePayload(ToDict):
    disco: str
    meterNo: str
    type: str
    amount: float
    phonenumber: str
    name: str
    address: str
    request_id: str
    serviceCode: str = "AOB"

@dataclass(init=False)
class ElectricityPurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from service provider"  # Default error message
    status: Optional[str] = "pending"  # Default to pending
    token: Optional[str] = None
    configure_token: Optional[str] = None
    reset_token: Optional[str] = None
    unit: Optional[str] = None
    tax_amount: Optional[float] = None
    token_amount: Optional[float] = None
    bonus_unit: Optional[str] = None
    bonus_token: Optional[str] = None
    debt_payment: Optional[str] = None
    minimum_amount: Optional[str] = None
    arrear: Optional[str] = None
    arrears_applied: Optional[str] = None
    amount: Optional[float] = None
    customer_name: Optional[str] = None
    customer_address: Optional[str] = None
    date: Optional[str] = None
    trans_id: Optional[str] = None
    disco: Optional[str] = None

#Airtime Purchase Payload/Response

@dataclass
class AirtimePurchasePayload(ToDict):
    phone: str
    amount: float
    network: str
    request_id: str
    vend_type: str = "VTU"
    serviceCode: str = "QAB"

@dataclass(init=False)
class AirtimePurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from service provider"  # Default error message
    status: Optional[str] = "pending"
    amount: Optional[str] = None
    transId: Optional[str] = None
    type: Optional[str] = None
    date: Optional[str] = None
    phone: Optional[str] = None



@dataclass
class DataBundle(ToDict, FieldMapper):
    price: Optional[int] = None
    code: Optional[float] = None
    allowance: Optional[str] = None
    validity: Optional[str] = None


@dataclass(init=False)
class DataBundleResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from service provider"
    status: Optional[str] = "failed"
    phone: Optional[str] = None
    network: Optional[str] = None
    product: Optional[List[DataBundle]] = None


#Data Vending Payload/Response

@dataclass(init=False)
class DataVendingPayload(ToDict):
    phone: str
    amount: float
    bundle: str
    network: str
    package: str
    request_id: str
    serviceCode: str = "BDA"

@dataclass(init=False)
class DataVendingResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from service provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    phone: Optional[str] = None
    package: Optional[str] = None


@dataclass
class ValidateIucNumberPayload(ToDict):
    smartCardNo: str
    type: str
    serviceCode: str = "GDS"


@dataclass(init=False)
class TvValidation(ToDict, FieldMapper):
    name: Optional[str] = None
    code: Optional[str] = None
    month: Optional[str] = None
    price: Optional[str] = None
    period: Optional[str] = None


@dataclass(init=False)
class TvValidationResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from service provider"
    status: Optional[str] = "failed"
    type: Optional[str] = None
    smartCardNo: Optional[str] = None
    customerName: Optional[str] = None
    accountStatus: Optional[str] = None
    dueDate: Optional[str] = None
    invoicePeriod: Optional[str] = None
    customerNumber: Optional[str] = None
    balance: Optional[str] = None
    customer_type: Optional[str] = None
    product: Optional[List[TvValidation]] = None

    def to_dict(self):
        return ValidatedCableDetails(iuc_number=self.smartCardNo,customer_name=self.customerName).to_dict()


@dataclass
class MultiChoicePackage:
    name: Optional[str] = None
    code: Optional[str] = None
    price: Optional[str] = None


@dataclass(init=False)
class MultichoiceCurrentSubscriptionResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    amount: Optional[str] = None
    smartcard_no: Optional[str] = None
    type: Optional[str] = None
    packages: Optional[List[MultiChoicePackage]] = None


@dataclass(init=False)
class DstvBoxOfficePurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None


@dataclass(init=False)
class VerifyMultichoiceAccountHasBoxOfficeResponse(ToDict, FieldMapper):
    message: str = "No response from provider"
    status: str = ("pending",)
    isBoxOffice: Optional[bool] = False
    type: Optional[str] = (None,)
    smartcard_no: Optional[str] = None


@dataclass(init=False)
class MultichoiceDueAmountAndDateResponse(ToDict, FieldMapper):
    message: str = "No response from provider"
    status: str = "pending"
    amount: Optional[str] = None
    smartcard_no: Optional[str] = None
    type: Optional[str] = None
    due_date: Optional[str] = None


@dataclass(init=False)
class MultichoicePaymentRenewalOrTenderAmountResponse(ToDict, FieldMapper):
    message: str = "No response from provider"
    status: str = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None


@dataclass
class DstvProductAddon:
    name: Optional[str] = None
    code: Optional[str] = None
    month: Optional[str] = None
    price: Optional[str] = None
    period: Optional[str] = None


@dataclass(init=False)
class DstvAddonResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    product: Optional[List[DstvProductAddon]] = None


@dataclass(init=False)
class DstvAddonPurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None

#Cable TV Purchase Payload/Response
@dataclass
class CableTVPurchasePayload(ToDict):
    amount: str
    type: str
    smartCardNo: str
    customerName: str
    packagename: str
    productsCode: str
    period: str
    request_id: str
    hasAddon: str = None
    serviceCode: str = "GDB"

@dataclass(init=False)
class CableTVPurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No resonse from provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None


@dataclass(init=False)
class GotvPurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None


@dataclass(init=False)
class StartimePurchaseResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None
    package: Optional[str] = None
    amount: Optional[str] = None


@dataclass(init=False)
class TVBouquetItem(ToDict, FieldMapper):
    name: Optional[str] = None
    code: Optional[str] = None
    duration: Optional[str] = None
    price: Optional[str] = None


@dataclass(init=False)
class TVBouquetResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    product: Dict[str, List[TVBouquetItem]] = None


@dataclass
class MultichoiceBouquetItem:
    name: Optional[str] = None
    code: Optional[str] = None
    month: Optional[str] = None
    price: Optional[str] = None
    period: Optional[str] = None


@dataclass(init=False)
class MultiChoiceBouquetResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    product: Optional[List[MultichoiceBouquetItem]] = None

#Bet validation Payload/Response

@dataclass
class BetAccountValidationPayload(ToDict):
    type: str
    customerId: str
    serviceCode: str = "BEV"

@dataclass(init=False)
class BetAccountValidationResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    name: Optional[str] = None
    type: Optional[str] = None
    customerId: Optional[str] = None
    reference: Optional[str] = None
    accountNumber: Optional[str] = None
    phoneNumber: Optional[str] = None
    emailAddress: Optional[str] = None
    canVend: Optional[str] = None
    minPayableAmount: Optional[int] = None
    charge: Optional[int] = None


#Bet Payment Payload/Response

@dataclass
class BetAccountPaymentPayload(ToDict):
    name: str
    customerId: str
    amount: str
    request_id: str

@dataclass(init=False)
class BetAccountPaymentResponse(ToDict, FieldMapper):
    message: Optional[str] = "No response from provider"
    status: Optional[str] = "pending"
    name: Optional[str] = None
    customerId: Optional[str] = None
    amount: Optional[str] = None
    transId: Optional[str] = None
    date: Optional[str] = None
    type: Optional[str] = None



# ----Request dataclasses------


@dataclass
class MultichoicePaymentRenewalOrTenderAmountRequest:
    smartCardNo: str
    customerName: str
    type: str
    amount: str
    packagename: str
    period: str
    request_id: str


@dataclass
class DstvBoxOfficePurchaseRequest:
    amount: str
    type: str
    smartCardNo: str
    customerName: str
    packagename: str
    boxOffice: bool
    request_id: str


@dataclass
class DstvAddonPurchaseRequest:
    smartCardNo: str
    customerName: str
    type: str
    amount: str
    packagename: str
    productsCode: str
    period: str
    hasAddon: str
    addonproductCode: str
    addonAmount: str
    addonproductName: str
    request_id: str
