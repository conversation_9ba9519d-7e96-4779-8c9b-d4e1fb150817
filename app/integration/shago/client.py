from dataclasses import asdict
from typing import Optional

from core import settings
from integration.base import BaseClient
from integration.shago.dto import (
    AirtimePurchasePayload,
    AirtimePurchaseResponse,
    BetAccountPaymentPayload,
    BetAccountPaymentResponse,
    BetAccountValidationPayload,
    BetAccountValidationResponse,
    CableTVPurchasePayload,
    CableTVPurchaseResponse,
    DataBundleResponse,
    DataVendingPayload,
    DataVendingResponse,
    DstvAddonPurchaseRequest,
    DstvAddonPurchaseResponse,
    DstvAddonResponse,
    DstvBoxOfficePurchaseRequest,
    DstvBoxOfficePurchaseResponse,
    ElectricityPurchasePayload,
    ElectricityPurchaseResponse,
    ElectricityValidationResponse,
    MultiChoiceBouquetResponse,
    MultichoiceCurrentSubscriptionResponse,
    MultichoiceDueAmountAndDateResponse,
    MultichoicePaymentRenewalOrTenderAmountRequest,
    MultichoicePaymentRenewalOrTenderAmountResponse,
    TVBouquetResponse,
    TvValidationResponse,
    ValidateIucNumberPayload,
    VerifyMultichoiceAccountHasBoxOfficeResponse, ValidateMeterPayload,
)


class ShagoClient(BaseClient):
    def __init__(self, timeout: Optional[int] = None, **kwargs):
        super().__init__()
        self.baseurl = settings.SHAGO_BASE_URL
        self.timeout = timeout or settings.DEFAULT_TIMEOUT
        api_key = settings.SHAGO_HASH_KEY

        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "hashKey": api_key,
        }

    def get_data_bundle(self, network: str) -> tuple[int, DataBundleResponse]:
        payload = {
            "serviceCode": settings.SERVICE_CODES["get_data_bundle"],
            "phone": None,
            "network": network,
        }
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )

        if not response:
            return status_code or 400, DataBundleResponse()
        return status_code, DataBundleResponse(**response)

    def validate_meter(self, payload: ValidateMeterPayload) -> tuple[int, ElectricityValidationResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, ElectricityValidationResponse()
        return status_code, ElectricityValidationResponse(**response)

    def purchase_electricity(self, payload: ElectricityPurchasePayload) -> tuple[int, ElectricityPurchaseResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, ElectricityPurchaseResponse()
        return status_code, ElectricityPurchaseResponse(**response)

    def purchase_airtime(self, payload: AirtimePurchasePayload) -> tuple[int, AirtimePurchaseResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, AirtimePurchaseResponse()
        return status_code, AirtimePurchaseResponse(**response)

    def data_vending(self, payload: DataVendingPayload) -> tuple[int, DataVendingResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, DataVendingResponse()
        return status_code, DataVendingResponse(**response)

    def tv_validation(self, payload: ValidateIucNumberPayload) -> tuple[int, TvValidationResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, TvValidationResponse()
        return status_code, TvValidationResponse(**response)

    def purchase_tv(self, payload: CableTVPurchasePayload) -> tuple[int, CableTVPurchaseResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, CableTVPurchaseResponse()
        return status_code, CableTVPurchaseResponse(**response)

    def get_current_multichoice_sub(self, type: str, action: str, smartCardNo: str) -> tuple[int, MultichoiceCurrentSubscriptionResponse]:
        payload = {
            "serviceCode": settings.SERVICE_CODES["get_current_multichoice_sub"],
            "type": type,
            "action": action,
            "smartCardNo": smartCardNo,
        }
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )
        if not response:
            return status_code or 400, MultichoiceCurrentSubscriptionResponse()
        return status_code, MultichoiceCurrentSubscriptionResponse(**response)

    def verify_multichoice_account_box_office(
        self, type: str, action: str, smartCardNo: str
    ) -> tuple[int, VerifyMultichoiceAccountHasBoxOfficeResponse]:
        payload = {
            "serviceCode": settings.SERVICE_CODES["get_current_multichoice_sub"],
            "type": type,
            "action": action,
            "smartCardNo": smartCardNo,
        }
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )
        if not response:
            return status_code or 400, VerifyMultichoiceAccountHasBoxOfficeResponse()
        return status_code, VerifyMultichoiceAccountHasBoxOfficeResponse(**response)

    def get_multichoice_due_amount_and_date(
        self,
        type: str,
        action: str,
        smartCardNo: str,
    ) -> tuple[int, MultichoiceDueAmountAndDateResponse]:
        payload = {
            "serviceCode": settings.SERVICE_CODES["get_current_multichoice_sub"],
            "type": type,
            "action": action,
            "smartCardNo": smartCardNo,
        }
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )
        if not response:
            return status_code or 400, MultichoiceDueAmountAndDateResponse()
        return status_code, MultichoiceDueAmountAndDateResponse(**response)

    def multichoice_payment_renewal_or_tender_amount(
        self, payload: MultichoicePaymentRenewalOrTenderAmountRequest
    ) -> tuple[int, MultichoicePaymentRenewalOrTenderAmountResponse]:
        request_payload = {
            "serviceCode": settings.SERVICE_CODES["multichoice_payment_renewal"],
            **asdict(payload),
        }
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=request_payload
        )
        if not response:
            return status_code or 400, MultichoicePaymentRenewalOrTenderAmountResponse()
        return status_code, MultichoicePaymentRenewalOrTenderAmountResponse(**response)

    def fetch_tv_addons(self, product_code: str) -> tuple[int, DstvAddonResponse]:
        payload = {"serviceCode": "MULTICHOICE","product_code": product_code,}
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )

        if not response:
            return status_code or 400, DstvAddonResponse()
        return status_code, DstvAddonResponse(**response)

    def tv_bouquet(self, type: str) -> tuple[int, TVBouquetResponse]:
        payload = {"serviceCode": "TV_PACKAGES", "type": type}
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )

        if not response:
            return status_code or 400, TVBouquetResponse()
        return status_code, TVBouquetResponse(**response)

    def multichoice_bouquet(self) -> tuple[int, MultiChoiceBouquetResponse]:
        payload = {"serviceCode": "MUL"}
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=payload
        )

        if not response:
            return status_code or 400, MultiChoiceBouquetResponse()
        return status_code, MultiChoiceBouquetResponse(**response)

    def bet_account_validation(self, payload: BetAccountValidationPayload) -> tuple[int, BetAccountValidationResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, BetAccountValidationResponse()
        return status_code, BetAccountValidationResponse(**response)

    def bet_account_payment(self, payload: BetAccountPaymentPayload) -> tuple[int, BetAccountPaymentResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.baseurl}", data=data
        )

        if not response:
            return status_code or 400, BetAccountPaymentResponse()
        return status_code, BetAccountPaymentResponse(**response)
