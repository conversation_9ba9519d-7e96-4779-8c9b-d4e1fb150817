from core import settings
from django.core.cache import cache
import requests
from integration.quickteller.dtos import <PERSON>rrorResponse, GetCategoriesResponse, GetBillerPaymentItemsResponse, \
    GetBillersByCategoryResponse, BillPaymentAdvicePayload, PaymentAdviceResponse, QueryTransactionResponse, \
    ValidateCustomerDetailsResponse
from integration.base import BaseClient


# Category IDs:
# - Electricity: 1
# - Cable TV: 2
# - Data: 3
# - Airtime: 4
#
# References:
# - Use `get_categories()` to verify Category IDs
# - Use `get_billers_by_category(category_id)` to fetch Biller IDs
# - Use `get_biller_payment_items(biller_id)` to fetch Payment Codes
class QuickTellerVASClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "QuickTeller"
    CACHE_KEY = "quick_teller:access_token"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.QUICKTELLER_BASE_URL
        self.terminal_id = settings.QUICKTELLER_TERMINAL_ID
        self.client_id = settings.QUICKTELLER_CLIENT_ID
        self.secret_key = settings.QUICKTELLER_SECRET_KEY
        self.token_url = settings.QUICKTELLER_TOKEN_URL

        token = self.__get_access_token()

        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }

    def __get_access_token(self):
        token = cache.get(self.CACHE_KEY)
        if token:
            return token

        response = requests.post(
            self.token_url,
            data={'grant_type': 'client_credentials', 'scope': 'profile'},
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            auth=(self.client_id, self.secret_key)
        )

        if not response.ok:
            message = response.text
            raise RuntimeError(f"Unable to generate token. Reason: {message}")

        data = response.json()
        access_token = data['access_token']
        expires_in = data['expires_in']

        # Store in cache 60 seconds less than actual expiry
        cache.set(self.CACHE_KEY, access_token, timeout=expires_in - 60)

        return access_token

    def get_categories(self) -> tuple[int, GetCategoriesResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v5/services/categories",
            data=None,
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, GetCategoriesResponse(**response)

    def get_billers_by_category(self, category_id: int) -> tuple[int, GetBillersByCategoryResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v5/services",
            params={'categoryId': category_id},
            data=None
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, GetBillersByCategoryResponse(**response)

    # Biller IDs by Network and Service:
    #
    # 9mobile:
    # - Data: 205 (preferred), 3961
    # - Airtime: 3341
    #
    # MTN:
    # - Data: 348
    # - Airtime: 109 (prepaid), 903 (postpaid)
    #
    # Airtel:
    # - Data: 2775, 2774
    # - Airtime: 901 (prepaid), 617 (postpaid)
    #
    # Glo:
    # - Data: 3070
    # - Airtime: 913
    def get_biller_payment_items(self, biller_id: str) -> tuple[int, GetBillerPaymentItemsResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v5/services/options",
            params={'serviceid': biller_id},
            data=None
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, GetBillerPaymentItemsResponse(**response)

    def bill_payment_advice(self, payload: BillPaymentAdvicePayload) -> tuple[
        int, PaymentAdviceResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v5/Transactions",
            data=payload.to_dict(),
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, PaymentAdviceResponse(**response)

    def query_transaction(self, reference: str) -> tuple[int, QueryTransactionResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="GET",
            url=f"{self.base_url}/v5/Transactions",
            params={'requestRef': reference},
            data=None
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, QueryTransactionResponse(**response)

    def validate_customer_details(self, customer_id: str, payment_code: str) -> tuple[
        int, ValidateCustomerDetailsResponse | ErrorResponse]:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v5/Transactions/validatecustomers",
            data={
                "customers": [
                    {
                        "PaymentCode": payment_code,
                        "CustomerId": customer_id,
                    }
                ],
                "TerminalId": self.terminal_id,
            },
        )

        if not response:
            return status_code or 400, ErrorResponse()

        return status_code, ValidateCustomerDetailsResponse(**response)
