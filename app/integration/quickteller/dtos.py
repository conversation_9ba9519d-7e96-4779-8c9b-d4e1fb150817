from dataclasses import dataclass
from typing import Optional, List, Any
from integration.utils import ToDict, FieldMapper


@dataclass(init=False)
class ErrorResponse(ToDict, FieldMapper):
    ResponseCode: Optional[str] = None
    Message: Optional[str] = None
    ResponseData: Optional[list] = None
    ResponseCodeGrouping: Optional[str] = None
    MessageDetail: Optional[str] = None
    ResponseDescription: Optional[str] = None


@dataclass(init=False)
class BillerCategoriesData(ToDict, FieldMapper):
    Id: Optional[int] = None
    Name: Optional[str] = None
    Description: Optional[str] = None
    Billers: Optional[List[Any]] = None


@dataclass(init=False)
class GetCategoriesResponse(ToDict, FieldMapper):
    ResponseCode: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    BillerCategories: Optional[List[BillerCategoriesData]] = None


@dataclass(init=False)
class PaymentItemsData:
    Id: Optional[str] = None
    Name: Optional[str] = None
    BillerName: Optional[bool] = None
    ConsumerIdField: Optional[str] = None
    Code: Optional[str] = None
    BillerType: Optional[str] = None
    ItemFee: Optional[str] = None
    Amount: Optional[str] = None
    BillerId: Optional[str] = None
    BillerCategoryId: Optional[str] = None
    CurrencyCode: Optional[str] = None
    CurrencySymbol: Optional[str] = None
    ItemCurrencySymbol: Optional[str] = None
    Children: Optional[List[Any]] = None
    IsAmountFixed: Optional[str] = None
    PaymentCode: Optional[str] = None


@dataclass(init=False)
class GetBillerPaymentItemsResponse(ToDict, FieldMapper):
    ResponseCode: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    PaymentItems: Optional[List[PaymentItemsData]] = None


@dataclass(init=False)
class GetBillersByCategoryResponse(ToDict, FieldMapper):
    ResponseCode: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    BillerList: Optional[List[Any]] = None


# Bill Payment Payload and Response
@dataclass()
class BillPaymentAdvicePayload(ToDict):
    paymentCode: str
    customerId: str
    customerMobile: str
    customerEmail: str
    amount: str | float
    requestReference: str


from dataclasses import dataclass
from typing import Optional


@dataclass(init=False)
class AdditionalInfo(ToDict, FieldMapper):
    Pin: Optional[str] = None
    vatAmount: Optional[str] = None
    tariffClass: Optional[str] = None
    tariff: Optional[str] = None
    arrears: Optional[str] = None
    unitsIssued: Optional[str] = None
    units: Optional[str] = None
    costOfUnits: Optional[str] = None
    token: Optional[str] = None
    receiptNo: Optional[str] = None
    dealerName: Optional[str] = None
    agentName: Optional[str] = None
    agentCode: Optional[str] = None
    customerCareNo: Optional[str] = None
    OrderNumber: Optional[str] = None
    Address: Optional[str] = None
    AccountType: Optional[str] = None
    ServiceAddress: Optional[str] = None
    MinimumPayable: Optional[str] = None


@dataclass(init=False)
class PaymentAdviceResponse(ToDict, FieldMapper):
    TransactionRef: Optional[str] = None
    ApprovedAmount: Optional[str] = None
    ResponseCode: Optional[str] = None
    ResponseDescription: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    AdditionalInfo: Optional[AdditionalInfo] = None


@dataclass(init=False)
class BillPaymentData(ToDict, FieldMapper):
    Biller: Optional[str] = None
    CustomerId1: Optional[str] = None
    PaymentTypeName: Optional[str] = None
    RechargePin: Optional[str] = None
    paymentTypeCode: Optional[str] = None
    NarrationStreet: Optional[str] = None
    NarrationCity: Optional[str] = None
    ReceivingInstitutionId: Optional[str] = None
    ServiceProvider: Optional[str] = None
    ServiceName: Optional[str] = None
    Payee: Optional[str] = None


@dataclass(init=False)
class QueryTransactionResponse(ToDict, FieldMapper):
    TransactionId: Optional[int] = None
    ServiceProviderId: Optional[str] = None
    ServiceCode: Optional[str] = None
    ServiceName: Optional[str] = None
    TransactionRef: Optional[str] = None
    RequestReference: Optional[str] = None
    Status: Optional[str] = None
    TransactionSet: Optional[str] = None
    TransactionResponseCode: Optional[str] = None
    PaymentDate: Optional[str] = None
    Amount: Optional[str] = None
    Surcharge: Optional[str] = None
    CurrencyCode: Optional[str] = None
    Customer: Optional[str] = None
    CustomerEmail: Optional[str] = None
    CustomerMobile: Optional[str] = None
    Recharge: Optional[Any] = None
    ResponseCode: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    BillPayment: Optional[BillPaymentData] = None
    MiscData: Optional[str] = None

@dataclass(init=False)
class CustomerData:
    BillerId: Optional[Any] = None
    PaymentCode: Optional[str] = None
    CustomerId: Optional[str] = None
    ResponseCode: Optional[str] = None
    FullName: Optional[str] = None
    AmountTypeDescription: Optional[str] = None
    ResponseDescription: Optional[str] = None
    Amount: Optional[Any] = None
    AmountType: Optional[Any] = None
    Surcharge: Optional[Any] = None

@dataclass(init=False)
class ValidateCustomerDetailsResponse(ToDict, FieldMapper):
    ResponseCode: Optional[str] = None
    ResponseCodeGrouping: Optional[str] = None
    Customers: Optional[List[CustomerData]] = None