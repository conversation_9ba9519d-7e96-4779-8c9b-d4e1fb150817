from dataclasses import fields
from typing import Optional

from core import settings
from integration.base import BaseClient
from integration.gracehub.dto import DataTopUpResponse


class GraceHubClient(BaseClient):
    def __init__(self, timeout: Optional[int] = None, **kwargs):
        super().__init__()
        self.baseurl = settings.GRACEHUB_BASE_URL
        self.timeout = timeout or settings.DEFAULT_TIMEOUT
        authorization = settings.GRACEHUB_AUTH

        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": authorization,
        }

    def data_top_up(
        self, network: int, mobile_number: str, plan: str, ported_number: bool
    ) -> Optional[DataTopUpResponse]:
        payload = {
            "network": network,
            "mobile_number": mobile_number,
            "plan": plan,
            "ported_number": ported_number,
        }
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.baseurl}/data/topup",
            data=payload,
        )
        validated_keys = {f.name for f in fields(DataTopUpResponse)}
        filtered_response = {k: v for k, v in response.items() if k in validated_keys}
        return filtered_response, status_code
