from core import settings
from integration.base import BaseClient
from integration.youverify.dtos import (
    AdvancedPhoneNumberSearchResponse,
    BVNVerificationResponse,
    NINVerificationResponse,
)


class YouVerifyClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "YouVerify"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.YOU_VERIFY_BASE_URL
        self.api_key = settings.YOU_VERIFY_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "token": self.api_key,
        }

    def advanced_phone_number_search(
        self, mobile: str
    ) -> [int, AdvancedPhoneNumberSearchResponse]:
        data = {"mobile": mobile, "isSubjectConsent": True}
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/v2/api/identity/ng/nin-phone",
            data=data,
        )

        if not response:
            return status_code or 400, AdvancedPhoneNumberSearchResponse()

        return status_code, AdvancedPhoneNumberSearchResponse(**response)

    def bvn_verification(self, bvn: str) -> [int, BVNVerificationResponse]:
        data = {"id": bvn, "isSubjectConsent": True}
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/v2/api/identity/ng/bvn", data=data
        )

        if not response:
            return status_code or 400, BVNVerificationResponse()

        return status_code, BVNVerificationResponse(**response)

    def nin_verification(self, nin: str) -> [int, NINVerificationResponse]:
        data = {"id": nin, "isSubjectConsent": True}
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/v2/api/identity/ng/nin", data=data
        )

        if not response:
            return status_code or 400, NINVerificationResponse()

        return status_code, NINVerificationResponse(**response)
