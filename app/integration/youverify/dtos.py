from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, Optional, Dict

from common.utils import format_date, parse_gender
from integration.utils import <PERSON><PERSON>ap<PERSON>, ToDict

"""
Advanced phone number search response
"""


@dataclass(init=False)
class AddressData(ToDict, FieldMapper):
    town: Optional[str] = None
    lga: Optional[str] = None
    state: Optional[str] = None
    addressLine: Optional[str] = None


@dataclass(init=False)
class PhoneData(ToDict, FieldMapper):
    id: Optional[str]
    address: Optional[AddressData]
    parentId: Optional[str]
    status: Optional[str]
    reason: Optional[str]
    dataValidation: Optional[bool]
    selfieValidation: Optional[bool]
    firstName: Optional[str]
    middleName: Optional[str]
    lastName: Optional[str]
    image: Optional[str]
    mobile: Optional[str]
    birthState: Optional[str]
    birthLGA: Optional[str]
    birthCountry: Optional[str]
    dateOfBirth: Optional[str]
    isConsent: Optional[bool]
    nin: Optional[str]
    idNumber: Optional[str]
    businessId: Optional[str]
    type: Optional[str]
    allValidationPassed: Optional[bool]
    requestedAt: Optional[str]
    requestedById: Optional[str]
    country: Optional[str]
    createdAt: Optional[str]
    lastModifiedAt: Optional[str]
    metadata: Optional[dict]
    requestedBy: Optional[Any]


@dataclass(init=False)
class AdvancedPhoneNumberSearchResponse(ToDict, FieldMapper):
    success: Optional[bool] = False
    statusCode: Optional[int] = None
    message: Optional[str] = "Unable to complete request"
    links: Optional[List[Any]] = None
    data: Optional[PhoneData] = None


"""
Verify BVN response
"""


@dataclass(init=False)
class BvnAddressData(ToDict, FieldMapper):
    town: Optional[str] = None
    lga: Optional[str] = None
    state: Optional[str] = None
    addressLine: Optional[str] = None


@dataclass(init=False)
class BvnRequestedByData(ToDict, FieldMapper):
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    middleName: Optional[str] = None
    id: Optional[str] = None


@dataclass(init=False)
class BVNData(ToDict, FieldMapper):
    id: Optional[str] = None
    parentId: Optional[str] = None
    status: Optional[str] = None
    reason: Optional[str] = None
    dataValidation: Optional[bool] = None
    selfieValidation: Optional[bool] = None
    firstName: Optional[str] = None
    middleName: Optional[str] = None
    lastName: Optional[str] = None
    image: Optional[str] = None
    enrollmentBranch: Optional[str] = None
    enrollmentInstitution: Optional[str] = None
    mobile: Optional[str] = None
    dateOfBirth: str = None
    isConsent: Optional[bool] = None
    idNumber: Optional[str] = None
    nin: Optional[str] = None
    shouldRetrivedNin: Optional[bool] = None
    businessId: Optional[str] = None
    type: Optional[str] = None
    allValidationPassed: Optional[bool] = None
    requestedAt: Optional[str] = None
    requestedById: Optional[str] = None
    country: Optional[str] = None
    createdAt: Optional[str] = None
    lastModifiedAt: Optional[str] = None
    email: Optional[str] = None
    registrationDate: Optional[str] = None
    gender: str = None
    levelOfAccount: Optional[str] = None
    address: Optional[BvnAddressData] = None
    title: Optional[str] = None
    maritalStatus: Optional[str] = None
    lgaOfOrigin: Optional[str] = None
    otherMobile: Optional[str] = None
    stateOfOrigin: Optional[str] = None
    watchListed: Optional[bool] = None
    nameOnCard: Optional[str] = None
    fullDetails: Optional[bool] = None
    metadata: Optional[List[Any]] = None
    requestedBy: Optional[BvnRequestedByData] = None


@dataclass(init=False)
class BVNVerificationResponse(ToDict, FieldMapper):
    success: Optional[bool] = False
    status_code: Optional[int] = None
    message: Optional[str] = "Unable to complete request"
    links: Optional[List[Any]] = None
    data: Optional[BVNData] = None

    def to_dict(self) -> dict:

        first_name = self.data.firstName
        middle_name = self.data.middleName
        last_name = self.data.lastName
        date_of_birth = self.data.dateOfBirth

        return {
            "bvn_number": self.data.idNumber,
            "name_on_card": None,
            "enrolment_branch": self.data.enrollmentBranch,
            "enrolment_bank": self.data.enrollmentInstitution,
            "formatted_registration_date": self.data.registrationDate,
            "level_of_account": self.data.levelOfAccount,
            "nin": self.data.nin,
            "watchlisted": self.data.watchListed,
            "verification_status": "VERIFIED",
            "personal_info": {
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "full_name": f"{first_name or ''} {middle_name or ''} {last_name or ''}".strip().upper(),
                "email": self.data.email,
                "gender": parse_gender(self.data.gender),
                "phone_number": self.data.mobile,
                "phone_number_2": None,
                "date_of_birth": format_date(date_of_birth),
                "formatted_date_of_birth": format_date(date_of_birth),
                "lga_of_origin": self.data.lgaOfOrigin,
                "state_of_origin": self.data.stateOfOrigin,
                "nationality": "NIGERIAN",
                "marital_status": self.data.maritalStatus,
                "image": self.data.image,
            },
            "residential_info": {
                "state_of_residence": None,
                "lga_of_residence": None,
                "residential_address": self.data.address,
            },
        }


"""
Verify NIN response
"""


@dataclass(init=False)
class NinRequestedByData(ToDict, FieldMapper):
    firstName: Optional[str]
    lastName: Optional[str]
    middleName: Optional[str]
    id: Optional[str]


@dataclass(init=False)
class NinData(ToDict, FieldMapper):
    id: Optional[str]
    address: Optional[AddressData]
    parentId: Optional[str]
    status: Optional[str]
    reason: Optional[str]
    dataValidation: Optional[bool]
    selfieValidation: Optional[bool]
    firstName: Optional[str]
    middleName: Optional[str]
    lastName: Optional[str]
    image: Optional[str]
    signature: Optional[str]
    gender: Optional[str]
    mobile: Optional[str]
    email: Optional[str]
    birthState: Optional[str]
    nokState: Optional[str]
    religion: Optional[str]
    birthLGA: Optional[str]
    birthCountry: Optional[str]
    dateOfBirth: Optional[str]
    isConsent: Optional[bool]
    idNumber: Optional[str]
    businessId: Optional[str]
    type: Optional[str]
    allValidationPassed: Optional[bool]
    requestedAt: Optional[str]
    requestedById: Optional[str]
    country: Optional[str]
    createdAt: Optional[str]
    lastModifiedAt: Optional[str]
    metadata: Optional[List[Any]]
    requestedBy: Optional[NinRequestedByData]


@dataclass(init=False)
class NINVerificationResponse(ToDict, FieldMapper):
    success: Optional[bool] = False
    statusCode: Optional[int] = None
    message: Optional[str] = "Unable to complete request"
    links: Optional[List[Any]] = None
    data: Optional[NinData] = None

    def to_dict(self) -> dict:
        first_name = self.data.firstName
        middle_name = self.data.middleName
        last_name = self.data.lastName
        date_of_birth = self.data.dateOfBirth

        return {
            "nin_number": self.data.idNumber,
            "document_no": None,
            "verification_status": "VERIFIED",
            "service_type": "NIN_VERIFICATION",
            "personal_info": {
                "title": None,
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "full_name": f"{first_name or ''} {middle_name or ''} {last_name or ''}",
                "email": self.data.email,
                "gender": parse_gender(self.data.gender),
                "phone_number": self.data.mobile,
                "date_of_birth": format_date(date_of_birth),
                "formatted_date_of_birth": format_date(date_of_birth),
                "lga_of_origin": self.data.birthLGA,
                "state_of_origin": self.data.birthState,
                "nationality": "NIGERIAN",
                "marital_status": None,
                "height": None,
                "religion": self.data.religion,
                "native_language": None,
                "other_language": None,
                "image": self.data.image,
            },
            "next_of_kin": {
                "firstname": None,
                "surname": None,
                "address": None,
                "lga": None,
                "town": None,
                "state": None,
            },
            "residential_info": {
                "state_of_residence": self.data.address.state,
                "lga_of_residence": self.data.address.lga,
                "address": self.data.address.addressLine,
                "residence_status": None,
            },
            "indigene_info": {
                "lga_of_origin": None,
                "place_of_origin": None,
                "state_of_origin": None,
                "lga_of_birth": self.data.birthLGA,
                "state_of_birth": self.data.birthState,
                "country_of_birth": self.data.birthCountry
            },
            "education_profession": {
                "educational_level": None,
                "employment_status": None,
                "profession": None,
            },
        }

