from dataclasses import dataclass, field
from typing import Any, List, Optional

from core import settings
from integration.utils import FieldMapper, ToDict

"""
Verify Candidate Payload/Response
"""


@dataclass
class VerifyCandidatePayload(ToDict):
    ProfileCode: str
    GSMNo: str
    ServiceType: str
    isMockCandidate: bool
    SellingPoint: str = field(default_factory=lambda: settings.JAMB_SELLING_POINT)
    Proof: str = field(default_factory=lambda: settings.JAMB_SECURITY_TOKEN)


@dataclass(init=False)
class CardInfoData(ToDict, FieldMapper):
    Surname: Optional[str] = None
    FirstName: Optional[str] = None
    MiddleName: Optional[str] = None
    GSMNo: Optional[str] = None
    Amount: Optional[float] = None
    isNotUnderAge: Optional[bool] = None
    PaymentBreakDown: Optional[List[Any]] = None


@dataclass(init=False)
class VerifyCandidateResponse(ToDict, FieldMapper):
    Success: Optional[bool] = False
    ErrorMessage: Optional[str] = None
    ProfileCode: Optional[str] = None
    candInfoDetails: Optional[CardInfoData] = None


"""
Vend Pin Payload/Response
"""


@dataclass
class VendPinPayload(ToDict):
    ProfileCode: str
    GSMNo: str
    ServiceType: str
    isMockCandidate: bool
    isBrilliant: str
    SellingPoint: str = field(default_factory=lambda: settings.JAMB_SELLING_POINT)
    Proof: str = field(default_factory=lambda: settings.JAMB_SECURITY_TOKEN)


@dataclass(init=False)
class PinInfoData(ToDict, FieldMapper):
    PIN: Optional[bool] = None
    Surname: Optional[str] = None
    FirstName: Optional[str] = None
    MiddleName: Optional[str] = None
    GSMNo: Optional[str] = None
    Amount: Optional[float] = None
    PaymentBreakDown: Optional[List[Any]] = None


@dataclass(init=False)
class VendPinResponse(ToDict, FieldMapper):
    Success: Optional[bool] = False
    ErrorMessage: Optional[str] = None
    ProfileCode: Optional[str] = None
    pinInfoDetails: Optional[PinInfoData] = None
