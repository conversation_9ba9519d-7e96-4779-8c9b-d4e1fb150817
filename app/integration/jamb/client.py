from core import settings
from integration.base import BaseClient
from integration.jamb.dtos import (
    VendPinPayload,
    VendPinResponse,
    VerifyCandidatePayload,
    VerifyCandidateResponse,
)


class JambClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "JAMB"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.JAMB_BASE_URL
        self.security_token = settings.JAMB_SECURITY_TOKEN
        self.security_point = settings.JAMB_SELLING_POINT
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def verify_candidate(
        self, payload: VerifyCandidatePayload
    ) -> [int, VerifyCandidateResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/GetCandidateNamesForVending", data=data
        )

        if not response:
            return status_code or 400, VerifyCandidateResponse()

        return status_code, VerifyCandidateResponse(**response)

    def vend_pin(self, payload: VendPinPayload) -> [int, VendPinResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/ProcureCandidatesPIN", data=data
        )

        if not response:
            return status_code or 400, VendPinResponse()

        return status_code, VendPinResponse(**response)
