import logging
import time

import requests
from django.conf import settings
from django.utils import timezone
from pykolofinance.audtilog.contrib import mask_sensitive_data
from pykolofinance.audtilog.tasks import send_logs_to_logger
from requests.adapters import HTTPAdapter, Retry
from urllib3.exceptions import ReadTimeoutError

logger = logging.getLogger(__name__)


class BaseClient:
    headers = {}
    timeout = 30
    ClientName = None
    status_code = None

    def __init__(self):
        # Setting up retry policy for transient errors
        self.session = requests.Session()
        retries = Retry(
            total=0, backoff_factor=0, status_forcelist=[500, 502, 503, 504]
        )
        self.session.mount("https://", HTTPAdapter(max_retries=retries))
        self.response = {}

    def _make_request(self, method: str, url: str, data, params=None):
        """
        Helper method to make an HTTP request and handle common errors.
        Includes retry logic for transient errors and logs structured information.
        """
        try:
            self.start_time = time.time()
            logger.info(f"Making {method} request to {url} with data: {data}")
            if params:
                response = self.session.request(
                    method,
                    url,
                    data=data,
                    headers=self.headers,
                    timeout=self.timeout,
                    params=params,
                )
            else:
                response = self.session.request(
                    method, url, json=data, headers=self.headers, timeout=self.timeout
                )

            if response.status_code == 404:
                self.response = {"message": "Not found"}
                logger.error(
                    f"Resource not found when accessing {url}.",
                    extra={"url": url, "method": method},
                )
                self.status_code = 404
                return None, 404

            self.response = response.json()
            logger.info(mask_sensitive_data(self.response))

            self.status_code = response.status_code
            return response.json(), str(response.status_code)

        except ReadTimeoutError:
            self.response = {"message": "read timeout"}
            logger.error(
                f"Read timeout occurred when accessing {url}.",
                extra={"url": url, "method": method},
            )
            self.status_code = 408
            return None, 408

        except requests.exceptions.Timeout:
            self.response = {"message": "request timeout"}
            logger.error(
                f"Request to {url} timed out", extra={"url": url, "method": method}
            )
            self.status_code = 408
            return None, 408

        except requests.exceptions.HTTPError as http_err:
            status_code = (
                response.status_code if "response" in locals() and response else None
            )
            logger.error(
                f"HTTP error occurred: {http_err}",
                extra={"url": url, "status_code": status_code},
            )
            if response and response.content:
                try:
                    failed_response = response.json()
                    self.response = failed_response
                    logger.error(
                        f"Failed response: {failed_response}",
                        extra={"url": url, "status_code": status_code},
                    )
                    return None, status_code
                except ValueError:
                    pass  # Response content is not JSON
            self.status_code = status_code
            return None, status_code

        except requests.exceptions.RequestException as req_err:
            logger.error(
                f"Request exception: {req_err}", extra={"url": url, "method": method}
            )
            self.status_code = 500
            return None, 500

        except Exception as err:
            logger.error(
                f"An unexpected error occurred: {err}",
                extra={"url": url, "method": method},
            )
            self.status_code = 500
            return None, 500

        finally:
            try:
                if method.upper() in ["POST", "PUT"]:
                    data = data.copy() if data and isinstance(data, dict) else {}
                    headers = self.headers.copy()
                    headers.pop("X-API-KEY", None)
                    headers.pop("x-api-key", None)
                    data = dict(
                        app_name=self.ClientName,
                        api=mask_sensitive_data(url, mask_api_parameters=True),
                        headers=headers,
                        body=mask_sensitive_data(data),
                        method=method,
                        client_ip_address=url,
                        response=mask_sensitive_data(
                            self.response, mask_api_parameters=True
                        ),
                        status_code=self.status_code,
                        execution_time=time.time() - self.start_time,
                        added_on=str(timezone.now()),
                        account_number=(
                            data.get("account_number")
                            or data.get("accountNumber")
                            or data.get("AccountNumber")
                            or data.get("sourceAccountId")
                            if data
                            else None
                        ),
                        action_description=url,
                        action_function_call=url,
                        environment=settings.ENVIRONMENT_INSTANCE,
                    )
                    send_logs_to_logger.delay(data)
            except Exception as e:
                logger.error(f"Exception occurred while logging:{e}")
