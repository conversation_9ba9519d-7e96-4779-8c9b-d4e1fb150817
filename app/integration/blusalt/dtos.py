from dataclasses import dataclass
from typing import List, Optional

from common.utils import format_date, parse_gender
from integration.utils import FieldMapper, ToDict

"""
Verify BVN Payload/Response
"""


@dataclass
class VerifyBVNPayload(ToDict):
    phone_number: str
    bvn_number: str


@dataclass(init=False)
class ResidentialInfoData(ToDict, FieldMapper):
    state_of_residence: Optional[str] = None
    lga_of_residence: Optional[str] = None
    residential_address: Optional[str] = None


@dataclass(init=False)
class VerifyBVNPersonalInfoData(ToDict, FieldMapper):
    first_name: Optional[str] = None
    middle_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[str] = None
    gender: Optional[str] = None
    phone_number: Optional[str] = None
    phone_number_2: Optional[str] = None
    date_of_birth: Optional[str] = None
    formatted_date_of_birth: Optional[str] = None
    lga_of_origin: Optional[str] = None
    state_of_origin: Optional[str] = None
    nationality: Optional[str] = None
    marital_status: Optional[str] = None
    image_url: Optional[str] = None
    image_base64: Optional[str] = None


@dataclass(init=False)
class VerifyBVNResultData(ToDict, FieldMapper):
    request_reference: Optional[str] = None
    bvn_number: Optional[str] = None
    name_on_card: Optional[str] = None
    enrolment_branch: Optional[str] = None
    enrolment_bank: Optional[str] = None
    formatted_registration_date: Optional[str] = None
    level_of_account: Optional[str] = None
    nin: Optional[str] = None
    watchlisted: Optional[bool] = None
    verification_status: Optional[str] = None
    service_type: Optional[str] = None
    personal_info: Optional[VerifyBVNPersonalInfoData] = None
    residential_info: Optional[ResidentialInfoData] = None


@dataclass(init=False)
class VerifyBVNResponse(ToDict, FieldMapper):
    status_code: Optional[int] = None
    status: Optional[str] = None
    message: Optional[str] = None
    results: Optional[VerifyBVNResultData] = None

    def to_dict(self) -> dict:
        personal_info = self.results.personal_info
        residential_info = self.results.residential_info

        return {
            "bvn_number": self.results.bvn_number,
            "name_on_card": None,
            "service_type": "BVN_VERIFICATION",
            "enrolment_branch": self.results.enrolment_branch,
            "enrolment_bank": self.results.enrolment_bank,
            "formatted_registration_date": self.results.formatted_registration_date,
            "level_of_account": self.results.level_of_account,
            "nin": self.results.nin,
            "watchlisted": self.results.watchlisted,
            "verification_status": "VERIFIED",
            "personal_info": {
                "first_name": personal_info.first_name,
                "middle_name": personal_info.middle_name,
                "last_name": personal_info.last_name,
                "full_name": f"{personal_info.first_name or ''} {personal_info.middle_name or ''} {personal_info.last_name or ''}".strip(),
                "email": personal_info.email,
                "gender": parse_gender(personal_info.gender),
                "phone_number": personal_info.phone_number,
                "phone_number_2": personal_info.phone_number_2,
                "date_of_birth": format_date(personal_info.date_of_birth),
                "formatted_date_of_birth": format_date(personal_info.date_of_birth),
                "lga_of_origin": personal_info.lga_of_origin,
                "state_of_origin": personal_info.state_of_origin,
                "nationality": personal_info.nationality,
                "marital_status": personal_info.marital_status,
                "image": personal_info.image_base64,
            },
            "residential_info": {
                "state_of_residence": residential_info.state_of_residence,
                "lga_of_residence": residential_info.lga_of_residence,
                "residential_address": residential_info.residential_address,
            }
        }


"""
Verify NIN Payload/Response
"""


@dataclass
class VerifyNINPayload(ToDict):
    phone_number: str
    nin_number: str


@dataclass(init=False)
class PersonalInfoData(ToDict, FieldMapper):
    title: Optional[str] = None
    first_name: Optional[str] = None
    middle_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    maiden_name: Optional[str] = None
    gender: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    date_of_birth: Optional[str] = None
    formatted_date_of_birth: Optional[str] = None
    height: Optional[str] = None
    marital_status: Optional[str] = None
    image_url: Optional[str] = None
    image_base64: Optional[str] = None
    signature: Optional[str] = None
    native_language: Optional[str] = None
    other_language: Optional[str] = None
    religion: Optional[str] = None
    lga_of_origin: Optional[str] = None
    state_of_origin: Optional[str] = None
    nationality: Optional[str] = "NIGERIAN"


@dataclass(init=False)
class NextOfKinData(ToDict, FieldMapper):
    firstname: Optional[str] = None
    surname: Optional[str] = None
    address: Optional[str] = None
    lga: Optional[str] = None
    town: Optional[str] = None
    state: Optional[str] = None


@dataclass(init=False)
class ResidentialInfoData(ToDict, FieldMapper):
    address: Optional[str] = None
    lga_of_residence: Optional[str] = None
    state_of_residence: Optional[str] = None
    residence_status: Optional[str] = None


@dataclass(init=False)
class EducationProfessionData(ToDict, FieldMapper):
    educational_level: Optional[str] = None
    employment_status: Optional[str] = None
    profession: Optional[str] = None


@dataclass(init=False)
class IndigeneInfoData(ToDict, FieldMapper):
    lga_of_origin: Optional[str] = None
    place_of_origin: Optional[str] = None
    state_of_origin: Optional[str] = None
    lga_of_birth: Optional[str] = None
    state_of_birth: Optional[str] = None
    country_of_birth: Optional[str] = None


@dataclass(init=False)
class VerifyNINResultData(ToDict, FieldMapper):
    request_reference: Optional[str] = None
    nin_number: Optional[str] = None
    document_no: Optional[str] = None
    verification_status: Optional[str] = None
    service_type: Optional[str] = None
    personal_info: Optional[PersonalInfoData] = None
    next_of_kin: Optional[NextOfKinData] = None
    residential_info: Optional[ResidentialInfoData] = None
    education_profession: Optional[EducationProfessionData] = None
    indigene_info: Optional[IndigeneInfoData] = None


@dataclass(init=False)
class VerifyNINResponse(ToDict, FieldMapper):
    status_code: Optional[int] = None
    status: Optional[str] = None
    message: Optional[str] = None
    results: Optional[VerifyNINResultData] = None

    def to_dict(self) -> dict:
        personal_info = self.results.personal_info
        residential_info = self.results.residential_info
        indigene_info = self.results.indigene_info

        return {
            "nin_number": self.results.nin_number,
            "document_no": None,
            "verification_status": "VERIFIED",
            "service_type": "NIN_VERIFICATION",
            "personal_info": {
                "title": None,
                "first_name": personal_info.first_name,
                "middle_name": personal_info.middle_name,
                "last_name": personal_info.last_name,
                "full_name": f"{personal_info.first_name or ''} {personal_info.middle_name or ''} {personal_info.last_name or ''}".strip() if personal_info else None,
                "email": personal_info.email,
                "gender": personal_info.gender,
                "phone_number": personal_info.phone_number,
                "date_of_birth": format_date(personal_info.date_of_birth),
                "formatted_date_of_birth": format_date(personal_info.date_of_birth),
                "lga_of_origin": personal_info.lga_of_origin,
                "state_of_origin": personal_info.state_of_origin,
                "nationality": personal_info.nationality,
                "marital_status": None,
                "height": None,
                "religion": personal_info.religion,
                "native_language": None,
                "other_language": None,
                "image": personal_info.image_base64,
            },
            "next_of_kin": {
                "firstname": None,
                "surname": None,
                "address": None,
                "lga": None,
                "town": None,
                "state": None,
            },
            "residential_info": {
                "state_of_residence": residential_info.state_of_residence,
                "lga_of_residence": residential_info.lga_of_residence,
                "address": residential_info.address,
                "residence_status": residential_info.residence_status,
            },
            "indigene_info": {
                "lga_of_origin": indigene_info.lga_of_origin,
                "place_of_origin": None,
                "state_of_origin": indigene_info.state_of_origin,
                "lga_of_birth": indigene_info.lga_of_birth,
                "state_of_birth": indigene_info.state_of_birth,
                "country_of_birth": indigene_info.country_of_birth,
            },
            "education_profession": {
                "educational_level": None,
                "employment_status": None,
                "profession": None
            }
        }