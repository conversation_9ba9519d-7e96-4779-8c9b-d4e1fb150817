from core import settings
from integration.base import BaseClient
from integration.blusalt.dtos import VerifyBVNPayload, VerifyBVNResponse, VerifyNINPayload, VerifyNINResponse


class BlusaltClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "BLUSALT"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.BLUSALT_BASE_URL
        self.clientid = settings.BLUSALT_CLIENT_ID
        self.appname = settings.BLUSALT_APP_NAME
        self.apikey = settings.BLUSALT_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "clientid": self.clientid,
            "appname": self.appname,
            "apikey": self.apikey,
        }

    def verify_bvn(self, payload: VerifyBVNPayload) -> tuple[int, VerifyBVNResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/IdentityVerification/iBVN", data=data
        )

        if not response:
            return status_code or 400, VerifyBVNResponse()

        return status_code, VerifyBVNResponse(**response)

    def verify_nin(self, payload: VerifyNINPayload) -> tuple[int, VerifyNINResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST", url=f"{self.base_url}/IdentityVerification/NIN", data=data
        )

        if not response:
            return status_code or 400, VerifyNINResponse()

        return status_code, VerifyNINResponse(**response)
