from core import settings
from integration.base import BaseClient
from integration.capricorn.dtos import (
    PurchaseElectricityPayload,
    PurchaseElectricityResponse,
    ValidateMeterNumberPayload,
    ValidateMeterNumberResponse,
)


class CapricornClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "Capricorn"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.CAPRICORN_BASE_URL
        self.agent_id = settings.CAPRICORN_AGENT_ID
        self.x_api_key = settings.CAPRICORN_X_API_KEY
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-api-key": self.x_api_key,
        }

    def validate_meter_no(
        self, payload: ValidateMeterNumberPayload
    ) -> [int, ValidateMeterNumberResponse]:
        data = payload.to_dict()

        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/electricity/verify",
            data=data,
        )

        if not response:
            return status_code or 400, ValidateMeterNumberResponse()

        return status_code, ValidateMeterNumberResponse(**response)

    def purchase_electricity(
        self, payload: PurchaseElectricityPayload
    ) -> [int, PurchaseElectricityResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/electricity/request",
            data=data,
        )

        if not response:
            return status_code or 400, PurchaseElectricityResponse()

        return status_code, PurchaseElectricityResponse(**response)

    def fetch_electricity_billers(self):
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/electricity/billers",
            data=None,
        )

        return status_code, response
