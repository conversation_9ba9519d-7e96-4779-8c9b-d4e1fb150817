from dataclasses import dataclass, field
from typing import Any, List, Optional

from core import settings
from integration.utils import FieldMapper, ToDict

"""
Validate meter number payload/Response
"""


@dataclass
class ValidateMeterNumberPayload(ToDict):
    service_type: str
    account_number: str


@dataclass(init=False)
class RawOutputData(ToDict, FieldMapper):
    address: Optional[str] = None
    outstandingAmount: Optional[int] = None
    minimumAmount: Optional[int] = None
    name: Optional[str] = None
    customerAccountType: Optional[str] = None
    accountNumber: Optional[str] = None
    customerDtNumber: Optional[str] = None


@dataclass(init=False)
class UserData(ToDict, FieldMapper):
    name: Optional[str] = None
    address: Optional[str] = None
    outstandingBalance: Optional[str] = None
    accountNumber: Optional[str] = None
    minimumAmount: Optional[int] = None
    errorMessage: Optional[str] = None
    rawOutput: Optional[RawOutputData] = None


@dataclass(init=False)
class MeterData(ToDict, FieldMapper):
    user: Optional[UserData] = None


@dataclass(init=False)
class ValidateMeterNumberResponse(ToDict, FieldMapper):
    status: Optional[str] = None
    message: Optional[str] = None
    code: Optional[str] = None
    errors: Optional[List[Any]] = None
    data: Optional[MeterData] = None


"""
Purchase electricity payload/Response
"""


@dataclass
class PurchaseElectricityPayload(ToDict):
    service_type: str
    account_number: str
    amount: float
    phone: str
    agentReference: str
    agentId: field(default_factory=lambda: settings.CAPRICORN_AGENT_ID)


@dataclass(init=False)
class RawOutputData(ToDict, FieldMapper):
    status: Optional[str] = None
    tariff: Optional[str] = None
    penalty: Optional[float] = None
    meterCost: Optional[float] = None
    taxAmount: Optional[float] = None
    costOfUnit: Optional[float] = None
    debtAmount: Optional[float] = None
    resetToken: Optional[str] = None
    creditToken: Optional[str] = None
    tokenAmount: Optional[float] = None
    announcement: Optional[str] = None
    amountOfPower: Optional[str] = None
    currentCharge: Optional[float] = None
    lossOfRevenue: Optional[float] = None
    configureToken: Optional[str] = None
    tariffBaseRate: Optional[float] = None
    fixChargeAmount: Optional[float] = None
    installationFee: Optional[float] = None
    reconnectionFee: Optional[float] = None
    exchangeReference: Optional[str] = None
    meterServiceCharge: Optional[float] = None
    administrativeCharge: Optional[float] = None
    remainingDebt: Optional[float] = None
    outstandingDebt: Optional[float] = None
    balance: Optional[float] = None


@dataclass(init=False)
class TransactionData(ToDict, FieldMapper):
    statusCode: Optional[str]
    transactionStatus: Optional[str]
    transactionReference: Optional[str] = None
    transactionMessage: Optional[str] = None
    baxiReference: Optional[str] = None
    provider_message: Optional[str] = None
    tokenCode: Optional[str] = None
    tokenAmount: Optional[float] = None
    amountOfPower: Optional[str] = None
    creditToken: Optional[str] = None
    resetToken: Optional[str] = None
    configureToken: Optional[str] = None
    rawOutput: Optional[RawOutputData] = None


@dataclass(init=False)
class PurchaseElectricityResponse(ToDict, FieldMapper):
    message: Optional[str]
    code: Optional[int]
    data: Optional[TransactionData] = None
    status: Optional[str] = "pending"
