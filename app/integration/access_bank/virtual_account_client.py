from core import settings
from integration.access_bank.dtos import (
    AccessVAResponse,
    CheckTransactionStatusPayload,
    CustomerTransactionHistoryPayload,
    FetchCustomerDetailsPayload,
    GenerateVirtualAccountPayload,
    GetAccountsPayload,
    GetMerchantTransactionsPayload,
    GetTransactionsPayload,
    RenameVirtualAccountPayload,
    UpdateBVNPayload,
)
from integration.base import BaseClient


class AccessBankVirtualAccountClient(BaseClient):
    DEFAULT_TIMEOUT = 180
    ClientName = "AccessBank"

    def __init__(self):
        super().__init__()
        self.timeout = self.DEFAULT_TIMEOUT
        self.base_url = settings.ACCESS_BASE_URL
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Ocp-Apim-Subscription-Key": settings.ACCESS_SUBSCRIPTION_KEY,
            "Authorization": settings.ACCESS_AUTHORIZATION_KEY,
        }

    def generate_virtual_account(
        self, payload: GenerateVirtualAccountPayload
    ) -> tuple[int, AccessVAResponse]:
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/GenerateVirtualAccountNumber",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def rename_virtual_account(self, payload: RenameVirtualAccountPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/RenameVirtualAcct",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def update_bvn(self, payload: UpdateBVNPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/UpdateBVN",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def manage_account_request(self, payload: AccessVAResponse):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/ManageAccountRequest",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def check_transaction_status(self, payload: CheckTransactionStatusPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/CheckTransactionStatus",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def fetch_customer_details(self, payload: FetchCustomerDetailsPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/FetchCustomerDetails",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def customer_transaction_history(self, payload: CustomerTransactionHistoryPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/CustomerTransactionHistory",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def get_accounts(self, payload: GetAccountsPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/GetCustomers",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def get_merchant_transactions(self, payload: GetMerchantTransactionsPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/GetMerchantTransactions",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)

    def get_transactions(self, payload: GetTransactionsPayload):
        data = payload.to_dict()
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/virtualpaycorporate/GetTransactions",
            data=data,
        )

        if not response:
            return status_code or 400, AccessVAResponse()

        return status_code or 400, AccessVAResponse(**response)
