from dataclasses import dataclass
from typing import Any, Optional

from core import settings
from integration.utils import FieldMap<PERSON>, ToDict


@dataclass
class GenerateVirtualAccountPayload(ToDict):
    customer_id: str
    customer_name: str
    bvn: str
    customer_email: str
    customer_phone: str
    currency: str = "NGN"
    forcedebit: str = "N"
    request_authorizer: str = "System"
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class CheckTransactionStatusPayload(ToDict):
    request_id: str
    session_id: Optional[str] = None
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class CustomerTransactionHistoryPayload(ToDict):
    virtual_acc_no: str
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class FetchCustomerDetailsPayload(ToDict):
    customer_id: str
    account_no: Optional[str] = None
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class GetAccountsPayload(ToDict):
    page_number: Optional[str] = "1"
    page_size: Optional[str] = "20"
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class GetMerchantTransactionsPayload(ToDict):
    start_date: str
    end_date: str
    page_number: Optional[str] = "1"
    page_size: Optional[str] = "20"
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class GetTransactionsPayload(ToDict):
    start_date: str
    end_date: str
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class RenameVirtualAccountPayload(ToDict):
    virtual_acc_number: str
    customer_name: str
    new_customer_name: str
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass
class UpdateBVNPayload(ToDict):
    virtual_acc_number: str
    bvn: str
    merchant_id: str = settings.ACCESS_MERCHANT_ID
    channel_code: str = settings.ACCESS_CHANNEL_CODE


@dataclass(init=False)
class AccessVAResponse(ToDict, FieldMapper):
    response_code: Optional[str] = "XX"
    response_message: Optional[str] = "Unable to complete request"
    response_data: Optional[Any] = None
