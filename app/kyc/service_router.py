from typing import Optional, TypeVar, Union

from common.enums import VenderEnum
from kyc.contracts import (
    PhoneNumberLookupServiceInterface,
    VerifyBvnServiceInterface,
    VerifyNinServiceInterface,
)
from kyc.services.blusalt import BlusaltKycService
from kyc.services.you_verify import YouVerifyKycService

KycInterface = TypeVar(
    "KycInterface",
    bound=Union[
        VerifyBvnServiceInterface,
        VerifyNinServiceInterface,
        PhoneNumberLookupServiceInterface,
    ],
)


class KycServiceRouter:
    """
    Dynamically routes KYC operations to the correct vendor-specific implementation.

    This class abstracts away the logic of selecting the correct KYC service provider (e.g., YouVerify, Blusalt)
    based on the `driver` (vendor). It delegates all undefined method calls to the resolved KYC service,
    allowing the consumer to interact with a unified interface for operations like BVN/NIN verification
    and phone number lookup.
    """

    @staticmethod
    def resolve(driver: VenderEnum) -> KycInterface:
        """
        Resolves and returns an instance of the KYC service based on the current vendor.

        Returns:
            KycInterface: A concrete implementation of a KYC interface contract.
        """
        if driver == VenderEnum.YouVerify:
            return YouVerifyKycService()
        return BlusaltKycService()
