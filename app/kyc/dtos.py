from typing import Optional

from attr import dataclass

from common.utils import ToDict

@dataclass
class VerifyNinParams(ToDict):
    nin: str
    phone: Optional[str] = None
    reference: Optional[str] = None

@dataclass
class VerifyBvnParams(ToDict):
    bvn: str
    phone: Optional[str] = None
    reference: Optional[str] = None

@dataclass
class PhoneNumberLookupParams(ToDict):
    phone: str
    reference: Optional[str] = None
