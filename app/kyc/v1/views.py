from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON>ey
from common.dtos import ServiceResponseData
from kyc.v1.serializers import BvnVerificationSerializer, NinVerificationSerializer, PhoneNumberLookupSerializer


@extend_schema_view(
    verify_bvn=header,
    verify_nin=header,
    phone_number_lookup=header,
)
class KycViewSet(viewsets.GenericViewSet):
    http_method_names = ['post']
    permission_classes = [HasAPIKey]

    @action(
        methods=['POST'],
        serializer_class=BvnVerificationSerializer,
        detail=False,
        url_path="verify-bvn"
    )
    def verify_bvn(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)


    @action(
        methods=['POST'],
        serializer_class=NinVerificationSerializer,
        detail=False,
        url_path="verify-nin"
    )
    def verify_nin(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)


    @action(
        methods=['POST'],
        serializer_class=PhoneNumberLookupSerializer,
        detail=False,
        url_path="phone-number-lookup"
    )
    def phone_number_lookup(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)

