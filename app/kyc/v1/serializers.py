from rest_framework import serializers
from common.enums import VenderEnum, ProductEnum, ProviderEnum
from kyc.dtos import VerifyBvnParams, VerifyNinParams, PhoneNumberLookupParams
from kyc.service_router import KycServiceRouter
from provider.utils import get_vender


class BvnVerificationSerializer(serializers.Serializer):
    bvn = serializers.CharField()
    phone = serializers.CharField(required=False)
    reference = serializers.CharField(required=False)

    def save(self):
        params = VerifyBvnParams(**self.validated_data)
        vender = get_vender(ProductEnum.Kyc, ProviderEnum.Bvn)
        response = KycServiceRouter().resolve(vender).verify_bvn(params)

        return response

class NinVerificationSerializer(serializers.Serializer):
    nin = serializers.CharField()
    phone = serializers.CharField(required=False)
    reference = serializers.CharField(required=False)

    def save(self):
        params = VerifyNinParams(**self.validated_data)
        vender = get_vender(ProductEnum.Kyc, ProviderEnum.Nin)
        response = KycServiceRouter().resolve(vender).verify_nin(params)

        return response


class PhoneNumberLookupSerializer(serializers.Serializer):
    phone = serializers.CharField()
    reference = serializers.CharField(required=False)

    def save(self):
        params = PhoneNumberLookupParams(**self.validated_data)
        vender = get_vender(ProductEnum.Kyc, ProviderEnum.PhoneNumberLookup)
        response = KycServiceRouter().resolve(vender).phone_number_lookup(params)

        return response