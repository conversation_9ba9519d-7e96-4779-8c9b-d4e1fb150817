from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from kyc.dtos import PhoneNumberLookupParams, VerifyBvnParams, VerifyNinParams


class VerifyNinServiceInterface(ABC):
    @abstractmethod
    def verify_nin(self, params: VerifyNinParams) -> ServiceResponseData:
        pass


class VerifyBvnServiceInterface(ABC):
    @abstractmethod
    def verify_bvn(self, params: VerifyBvnParams) -> ServiceResponseData:
        pass


class PhoneNumberLookupServiceInterface(ABC):
    @abstractmethod
    def phone_number_lookup(
        self, params: PhoneNumberLookupParams
    ) -> ServiceResponseData:
        pass
