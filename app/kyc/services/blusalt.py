import logging
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.blusalt.client import BlusaltClient
from integration.blusalt.dtos import VerifyBVNPayload, VerifyNINPayload
from kyc.contracts import VerifyBvnServiceInterface, VerifyNinServiceInterface
from kyc.dtos import VerifyBvnParams, VerifyNinParams

logger = logging.getLogger(__name__)


class BlusaltKycService(VerifyBvnServiceInterface, VerifyNinServiceInterface):

    def __init__(self):
        self.client = BlusaltClient()

    def verify_nin(self, params: VerifyNinParams) -> ServiceResponseData:
        try:
            payload = self.__create_verify_nin_payload(params)
            status_code, response = self.client.verify_nin(payload)
            return self.__handle_verification_response(status_code, response, "NIN verification")
        except Exception as e:
            logger.exception(f"NIN verification failed for {params.nin}: {str(e)}")
            return self.__create_error_response()

    def verify_bvn(self, params: VerifyBvnParams) -> ServiceResponseData:
        try:
            payload = self.__create_verify_bvn_payload(params)
            status_code, response = self.client.verify_bvn(payload)
            return self.__handle_verification_response(status_code, response, "BVN verification")
        except Exception as e:
            logger.exception(f"BVN verification failed for {params.bvn}: {str(e)}")
            return self.__create_error_response()

    @staticmethod
    def __handle_verification_response(status_code: int, response, operation: str) -> ServiceResponseData:
        if response.status == "success":
            return ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message=f"{operation} successful",
                data=response.to_dict(),
                status_code=status_code,
            )

        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message=response.message or f"{operation} failed",
            data=response.to_dict(),
            status_code=status_code,
        )

    @staticmethod
    def __create_error_response() -> ServiceResponseData:
        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message=GENERIC_EXCEPTION_ERROR_MESSAGE,
            data=None,
        )

    @staticmethod
    def __create_verify_bvn_payload(params: VerifyBvnParams) -> VerifyBVNPayload:
        return VerifyBVNPayload(
            bvn_number=params.bvn,
            phone_number=params.phone,
        )

    @staticmethod
    def __create_verify_nin_payload(params: VerifyNinParams) -> VerifyNINPayload:
        return VerifyNINPayload(
            nin_number=params.nin,
            phone_number=params.phone,
        )