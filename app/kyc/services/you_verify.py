import logging

from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.youverify.client import YouVerifyClient
from kyc.contracts import VerifyBvnServiceInterface, VerifyNinServiceInterface, PhoneNumberLookupServiceInterface
from kyc.dtos import PhoneNumberLookupParams, VerifyBvnParams, VerifyNinParams

logger = logging.getLogger(__name__)


class YouVerifyKycService(VerifyNinServiceInterface, VerifyBvnServiceInterface, PhoneNumberLookupServiceInterface):

    def __init__(self):
        self.client = YouVerifyClient()

    def verify_nin(self, params: VerifyNinParams) -> ServiceResponseData:
        try:
            status_code, response = self.client.nin_verification(params.nin)
            return self.__handle_verification_response(status_code, response, "NIN verification")
        except Exception as e:
            logger.exception(f"NIN verification failed for {params.nin}: {str(e)}")
            return self.__create_error_response()

    def verify_bvn(self, params: VerifyBvnParams) -> ServiceResponseData:
        try:
            status_code, response = self.client.bvn_verification(params.bvn)
            return self.__handle_verification_response(status_code, response, "BVN verification")
        except Exception as e:
            logger.exception(f"BVN verification failed for {params.bvn}: {str(e)}")
            return self.__create_error_response()

    def phone_number_lookup(self, params: PhoneNumberLookupParams) -> ServiceResponseData:
        try:
            status_code, response = self.client.advanced_phone_number_search(params.phone)
            return self.__handle_verification_response(status_code, response, "Phone number lookup")
        except Exception as e:
            logger.exception(f"Phone number lookup failed for {params.phone}: {str(e)}")
            return self.__create_error_response()

    @staticmethod
    def __handle_verification_response(status_code: int, response, operation: str) -> ServiceResponseData:
        if response.success:
            return ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message=f"{operation} successful",
                data=response.to_dict(),
                status_code=status_code,
            )

        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message=response.message,
            status_code=status_code,
        )

    @staticmethod
    def __create_error_response() -> ServiceResponseData:
        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message=GENERIC_EXCEPTION_ERROR_MESSAGE,
            data=None,
        )