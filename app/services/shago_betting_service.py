import logging
from typing import <PERSON><PERSON>

from integration.shago.client import <PERSON>hagoClient
from integration.shago.dto import BetAccountPaymentResponse, BetAccountValidationResponse
from integration.shago.utils import generate_request_id


class BetAccountService:
    def __init__(self):
        self.client = ShagoClient()

    def bet_account_validation(
        self, type: str, customerId: str
    ) -> <PERSON><PERSON>[BetAccountValidationResponse, int]:
        try:

            response, status_code = self.client.bet_account_validation(
                type=type, customerId=customerId
            )
            if not response:
                return BetAccountValidationResponse(), (
                    int(status_code) if status_code else 400
                )
            return BetAccountValidationResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during account validation: {e}")
            return None

    def bet_account_payment(
        self, reference: str, amount: str, customerId: str, name: str, type: str
    ) -> <PERSON><PERSON>[BetAccountPaymentResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.bet_account_payment(
                reference=reference,
                amount=amount,
                customerId=customerId,
                name=name,
                type=type,
                request_id=request_id,
            )
            if not response:
                return BetAccountPaymentResponse(), (
                    int(status_code) if status_code else 400
                )
            return BetAccountPaymentResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connections error during data vending:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error occur during data vending {e}")
            return None
