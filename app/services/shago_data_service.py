import logging
from typing import <PERSON><PERSON>

from integration.shago.client import <PERSON>ha<PERSON>C<PERSON>
from integration.shago.dto import DataBundleResponse, DataVendingResponse
from integration.shago.utils import generate_request_id


class DataService:
    def __init__(self):
        self.client = ShagoClient()

    def get_data_bundle(
        self, phone: str, network: str
    ) -> Tuple[DataBundleResponse, int]:
        try:

            response, status_code = self.client.get_data_bundle(
                phone=phone,
                network=network,
            )
            if not response:
                return DataBundleResponse(), int(status_code) if status_code else 400
            return DataBundleResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during data bundle lookup: {e}")
            return None

    def data_vending(
        self, phone: str, amount: str, bundle: str, network: str, package: str
    ) -> <PERSON><PERSON>[DataVendingResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.data_vending(
                phone=phone,
                amount=amount,
                bundle=bundle,
                network=network,
                package=package,
                request_id=request_id,
            )
            if not response:
                return DataVendingResponse(), int(status_code) if status_code else 400
            return DataVendingResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connections error during data vending:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error occur during data vending {e}")
            return None
