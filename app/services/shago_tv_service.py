import logging
from typing import <PERSON><PERSON>

from integration.shago.client import ShagoClient
from integration.shago.dto import (
    DstvAddonPurchaseResponse,
    DstvAddonResponse,
    DstvBoxOfficePurchaseResponse,
    DstvPurchaseResponse,
    GotvPurchaseResponse,
    MultiChoiceBouquetResponse,
    MultichoiceCurrentSubscriptionResponse,
    MultichoiceDueAmountAndDateResponse,
    MultichoicePaymentRenewalOrTenderAmountResponse,
    StartimePurchaseResponse,
    TVBouquetResponse,
    TvValidationResponse,
    VerifyMultichoiceAccountHasBoxOfficeResponse,
)
from integration.shago.utils import generate_request_id


class TVService:
    def __init__(self):
        self.client = ShagoClient()

    def tv_validation(
        self, smartCardNo: str, type: str
    ) -> Tuple[TvValidationResponse, int]:
        try:
            response, status_code = self.client.tv_validation(
                smartCardNo=smartCardNo,
                type=type,
            )
            if not response:
                return TvValidationResponse(), int(status_code) if status_code else 400
            return TvValidationResponse(**response), status_code
        except ValueError as ve:
            logging.error(f"Tv Validation error: {ve}")
            return None
        except ConnectionError as ce:
            logging.error(f"Connection error: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error in during TV validation: {e}")
            return None

    def dstv_purchase(
        self,
        smartCardNo: str,
        customerName: str,
        type: str,
        amount: str,
        packagename: str,
        productsCode: str,
        period: str,
        hasAddon: str,
    ) -> Tuple[DstvPurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.dstv_purchase(
                smartCardNo=smartCardNo,
                customerName=customerName,
                type=type,
                amount=amount,
                packagename=packagename,
                productsCode=productsCode,
                period=period,
                hasAddon=hasAddon,
                request_id=request_id,
            )
            if not response:
                return DstvPurchaseResponse(), int(status_code) if status_code else 400
            return DstvPurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error during dstv purchase: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error in during dstv purchase: {e}")
            return None

    def get_current_multichoice_sub(
        self, type: str, action: str, smartCardNo: str
    ) -> Tuple[MultichoiceCurrentSubscriptionResponse, int]:
        try:
            response, status_code = self.client.get_current_multichoice_sub(
                type=type, action=action, smartCardNo=smartCardNo
            )
            if not response:
                return MultichoiceCurrentSubscriptionResponse(), (
                    int(status_code) if status_code else 400
                )
            return MultichoiceCurrentSubscriptionResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error trying to get current multichoice subscription:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error trying to get currrent multichoice subscription:{e}"
            )
            return None

    def dstv_box_office_purchase(
        self,
        amount: str,
        type: str,
        smartCardNo: str,
        customerName: str,
        packagename: str,
        boxOffice: bool,
    ) -> Tuple[DstvBoxOfficePurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.dstv_box_office_purchase(
                request_id=request_id,
                amount=amount,
                type=type,
                smartCardNo=smartCardNo,
                customerName=customerName,
                packagename=packagename,
                boxOffice=boxOffice,
            )
            if not response:
                return DstvBoxOfficePurchaseResponse(), (
                    int(status_code) if status_code else 400
                )
            return DstvBoxOfficePurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error during dstv box office purchase:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during dstv box office purchase:{e}")
            return None

    def verify_multichoice_account_box_office(
        self, type: str, action: str, smartCardNo: str
    ) -> Tuple[VerifyMultichoiceAccountHasBoxOfficeResponse, int]:
        try:
            response, status_code = self.client.verify_multichoice_account_box_office(
                type=type,
                action=action,
                smartCardNo=smartCardNo,
            )
            if not response:
                return VerifyMultichoiceAccountHasBoxOfficeResponse(), (
                    int(status_code) if status_code else 400
                )
            return VerifyMultichoiceAccountHasBoxOfficeResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error during multichoice box office account verification:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error during multichoice box office account verification:{e}"
            )
            return None

    def get_multichoice_due_amount_and_date(
        self, type: str, action: str, smartCardNo: str
    ) -> Tuple[MultichoiceDueAmountAndDateResponse, int]:
        try:
            response, status_code = self.client.get_multichoice_due_amount_and_date(
                type=type,
                action=action,
                smartCardNo=smartCardNo,
            )
            if not response:
                return MultichoiceDueAmountAndDateResponse(), (
                    int(status_code) if status_code else 400
                )
            return MultichoiceDueAmountAndDateResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error. cannot return due date and amount:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error. cannot return due date and amount :{e}")
            return None

    def multichoice_payment_renewal_or_tender_amount(
        self,
        smartCardNo: str,
        customerName: str,
        type: str,
        amount: str,
        packagename: str,
        period: str,
    ) -> Tuple[MultichoiceDueAmountAndDateResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = (
                self.client.multichoice_payment_renewal_or_tender_amount(
                    smartCardNo=smartCardNo,
                    customerName=customerName,
                    type=type,
                    amount=amount,
                    packagename=packagename,
                    period=period,
                    request_id=request_id,
                )
            )
            if not response:
                return MultichoicePaymentRenewalOrTenderAmountResponse(), (
                    int(status_code) if status_code else 400
                )
            return (
                MultichoicePaymentRenewalOrTenderAmountResponse(**response),
                status_code,
            )
        except ConnectionError as ce:
            logging.error(f"Connection error. cannot return due date and amount:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error. cannot return due date and amount :{e}")
            return None

    def fetch_dstv_addons(self, product_code: str) -> Tuple[DstvAddonResponse, int]:
        try:
            response, status_code = self.client.fetch_dstv_addons(
                product_code=product_code
            )
            if not response:
                return DstvAddonResponse(), int(status_code) if status_code else 400
            return DstvAddonResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error while trying to get the product addons:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error whilr trying to get the product addons :{e}"
            )
            return None

    def purchase_dstv_addon(
        self,
        smartCardNo: str,
        customerName: str,
        type: str,
        amount: str,
        packagename: str,
        productsCode: str,
        period: str,
        hasAddon: str,
        addonproductCode: str,
        addonAmount: str,
        addonproductName: str,
    ) -> Tuple[DstvAddonPurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.purchase_dstv_addon(
                smartCardNo=smartCardNo,
                customerName=customerName,
                type=type,
                amount=amount,
                packagename=packagename,
                productsCode=productsCode,
                period=period,
                hasAddon=hasAddon,
                addonproductCode=addonproductCode,
                addonAmount=addonAmount,
                addonproductName=addonproductName,
                request_id=request_id,
            )
            if not response:
                return DstvAddonPurchaseResponse(), (
                    int(status_code) if status_code else 400
                )
            return DstvAddonPurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error while trying to purchase dstv addons:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error while trying to purchase dstv addons :{e}")
            return None

    def purchase_gotv(
        self,
        amount: str,
        type: str,
        smartCardNo: str,
        customerName: str,
        packagename: str,
        productsCode: str,
        period: str,
    ) -> Tuple[GotvPurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.purchase_gotv(
                request_id=request_id,
                amount=amount,
                type=type,
                smartCardNo=smartCardNo,
                customerName=customerName,
                packagename=packagename,
                productsCode=productsCode,
                period=period,
            )
            if not response:
                return GotvPurchaseResponse(), int(status_code) if status_code else 400
            return GotvPurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error while trying to purchase gotv subscription:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error while trying to purchase gotv subscription:{e}"
            )
            return None

    def purchase_startime(
        self,
        amount: str,
        type: str,
        smartCardNo: str,
        customerName: str,
        packagename: str,
    ) -> Tuple[StartimePurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.purchase_startime(
                request_id=request_id,
                amount=amount,
                type=type,
                smartCardNo=smartCardNo,
                customerName=customerName,
                packagename=packagename,
            )
            if not response:
                return StartimePurchaseResponse(), (
                    int(status_code) if status_code else 400
                )
            return StartimePurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error while trying to purchase startime subscription:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error while trying to purchase startime subscription:{e}"
            )
            return None

    def tv_bouqute(self, type: str) -> Tuple[TVBouquetResponse, int]:
        try:
            response, status_code = self.client.tv_bouquet(type=type)
            if not response:
                return TVBouquetResponse(), int(status_code) if status_code else 400
            return TVBouquetResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error while trying to get tv bouqute items:{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error while trying to get tv bouqute items:{e}")
            return None

    def multichoice_bouqute(self) -> Tuple[MultiChoiceBouquetResponse, int]:
        try:
            response, status_code = self.client.multichoice_bouquet()
            if not response:
                return MultiChoiceBouquetResponse(), (
                    int(status_code) if status_code else 400
                )
            return MultiChoiceBouquetResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(
                f"Connection error while trying to get multichoice bouqute items:{ce}"
            )
            return None
        except Exception as e:
            logging.error(
                f"Unexpected error while trying to get multichoice bouqute items:{e}"
            )
            return None
