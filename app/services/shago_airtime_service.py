import logging
from typing import <PERSON><PERSON>

from integration.shago.client import ShagoClient
from integration.shago.dto import AirtimePurchaseResponse
from integration.shago.utils import generate_request_id


class AirtimeService:
    def __init__(self):
        self.client = ShagoClient()

    def purchase_airtime(
        self, phone: str, amount: float, vend_type: str, network: str
    ) -> Tu<PERSON>[AirtimePurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.purchase_airtime(
                phone=phone,
                amount=amount,
                vend_type=vend_type,
                network=network,
                request_id=request_id,
            )
            if not response:
                return AirtimePurchaseResponse(), (
                    int(status_code) if status_code else 400
                )
            return AirtimePurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during airtime purchase: {e}")
            return None
