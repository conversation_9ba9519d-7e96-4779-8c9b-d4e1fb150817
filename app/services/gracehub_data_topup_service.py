import logging
from typing import <PERSON><PERSON>

from integration.gracehub.client import GraceHubClient
from integration.gracehub.dto import DataTopUpResponse


class DataTopUpService:
    def __init__(self):
        self.client = GraceHubClient()

    def data_top_up(
        self, network: int, mobile_number: str, plan: str, ported_number: bool
    ) -> Tuple[DataTopUpResponse, int]:
        try:
            response, status_code = self.client.data_top_up(
                network=network,
                mobile_number=mobile_number,
                plan=plan,
                ported_number=ported_number,
            )
            if not response:
                return DataTopUpResponse(), int(status_code) if status_code else 400
            return DataTopUpResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error during data top up: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during data top up: {e}")
            return None
