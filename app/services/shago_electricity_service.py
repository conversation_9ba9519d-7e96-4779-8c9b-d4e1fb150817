import logging
from typing import <PERSON><PERSON>

from integration.shago.client import ShagoClient
from integration.shago.dto import (
    ElectricityPurchaseResponse,
    ElectricityValidationResponse,
)
from integration.shago.utils import generate_request_id


class ElectricityService:
    def __init__(self):
        self.client = ShagoClient()

    def validate_meter(
        self, disco: str, meter_no: str, meter_type: str
    ) -> <PERSON><PERSON>[ElectricityValidationResponse, int]:
        try:
            response, status_code = self.client.validate_meter(
                disco=disco, meter_no=meter_no, meter_type=meter_type
            )
            if not response:
                return ElectricityValidationResponse(), (
                    int(status_code) if status_code else 400
                )
            return ElectricityValidationResponse(**response), status_code
        except ValueError as ve:
            logging.error(f"Validation error: {ve}")
            return None
        except ConnectionError as ce:
            logging.error(f"Connection error: {ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error in during meter validation: {e}")
            return None

    def purchase_electricity(
        self,
        disco: str,
        meter_no: str,
        meter_type: str,
        amount: float,
        phone_number: str,
        name: str,
        address: str,
    ) -> <PERSON><PERSON>[ElectricityPurchaseResponse, int]:
        try:
            request_id = generate_request_id()
            response, status_code = self.client.purchase_electricity(
                disco=disco,
                meter_no=meter_no,
                meter_type=meter_type,
                amount=amount,
                phone_number=phone_number,
                name=name,
                address=address,
                request_id=request_id,
            )
            if not response:
                return ElectricityPurchaseResponse(), (
                    int(status_code) if status_code else 400
                )
            return ElectricityPurchaseResponse(**response), status_code
        except ConnectionError as ce:
            logging.error(f"Connection error :{ce}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during Purchase:{e}")
            return None
