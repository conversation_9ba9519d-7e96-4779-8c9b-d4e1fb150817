#!/bin/sh

# celery -A core worker -c 4 --loglevel=info --logfile=logs/celery.log --detach
celery -A core worker --loglevel=info --concurrency=2 --hostname=worker1@%h --detach
#celery -A core worker --loglevel=info --concurrency=2 --hostname=worker2@%h --detach
#celery -A core worker --loglevel=info --concurrency=2 --hostname=worker3@%h --detach
#celery -A core worker --loglevel=info --concurrency=2 --hostname=worker4@%h --detach
#celery -A core worker --loglevel=info --concurrency=2 --hostname=worker5@%h --detach

celery -A core worker --loglevel=info --queues=logging_queue --concurrency=1 --hostname=logging_queue@%h --detach

#specific que for sms queue

celery -A core beat -l INFO --detach
celery -A core beat -l INFO --detach --scheduler django_celery_beat.schedulers:DatabaseScheduler

exec "$@"
