from rest_framework import serializers
from common.enums import VenderEnum, ProductEnum, ProviderEnum
from electricity.dtos import ElectricityPurchaseParams, ValidateMeterNumberParams
from electricity.enums import ElectricityBiller
from electricity.service_router import ElectricityServiceRouter
from provider.utils import get_vender


class ValidateMeterSerializer(serializers.Serializer):
    meter_number = serializers.CharField()
    biller = serializers.ChoiceField(choices=ElectricityBiller.choices())

    def validate(self, attrs):
        data = super().validate(attrs)
        data['biller'] = ElectricityBiller(data['biller'])
        return data

    def save(self):
        biller = ElectricityBiller(self.validated_data['biller'])
        params = ValidateMeterNumberParams(biller=biller, meter_number=self.validated_data['meter_number'])
        provider = params.biller.provider()
        vender = get_vender(ProductEnum.Electricity, provider)
        response = ElectricityServiceRouter().resolve(vender).validate(params)
        return response


class ElectricityPurchaseSerializer(serializers.Serializer):
    meter_number = serializers.CharField()
    amount = serializers.IntegerField()
    biller = serializers.ChoiceField(choices=ElectricityBiller.choices())
    phone = serializers.CharField()
    email = serializers.EmailField()
    reference = serializers.CharField()

    def validate(self, attrs):
        data = super().validate(attrs)
        data['biller'] = ElectricityBiller(data['biller'])
        if data['amount'] <= 0:
            raise serializers.ValidationError({'amount': 'Amount must be greater than zero.'})
        return data

    def save(self):
        params = ElectricityPurchaseParams(**self.validated_data)
        provider = params.biller.provider()
        vender = get_vender(ProductEnum.Electricity, provider)
        response = ElectricityServiceRouter().resolve(vender).purchase(params)

        return response