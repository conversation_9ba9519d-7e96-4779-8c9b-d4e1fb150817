from typing import Optional

from common.enums import VenderEnum
from electricity.contracts import ElectricityServiceInterface
from electricity.services.shago import ShagoElectricityService


class ElectricityServiceRouter:
    """
    A router class for handling different data purchase services.

    This class implements a method delegation pattern that forwards
    method calls to the appropriate driver implementation.
    """

    def __init__(self, driver: Optional[VenderEnum] = None):
        self.driver = driver

    def connector(self) -> ElectricityServiceInterface:
        """
        Returns the appropriate data purchase service based on the driver.

        Returns:
            DataPurchaseInterface: The data purchase service instance.
        """
        return ShagoElectricityService()

    def __getattr__(self, method: str):
        """
        Magic method delegates any undefined method calls to the connector.

        Args:
            method (str): The name of the method being called.

        Returns:
            Callable: Function that forwards arguments to connector's method.
        """

        def method_caller(*args, **kwargs):
            connector = self.connector()
            return getattr(connector, method)(*args, **kwargs)

        return method_caller
