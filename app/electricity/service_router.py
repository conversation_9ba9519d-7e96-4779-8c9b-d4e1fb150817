from typing import Optional

from common.enums import VenderEnum
from electricity.contracts import ElectricityServiceInterface
from electricity.services.shago import ShagoElectricityService


class ElectricityServiceRouter:
    """
    Routes electricity service requests to the appropriate vendor-specific implementation.

    This class acts as a dynamic service router that delegates electricity-related method calls
    (e.g., bill payment, meter validation) to the correct service implementation based on
    the provided `driver` (vendor). This abstraction allows the business logic to interact
    with a unified interface while enabling easy switching between different electricity service providers.
    Currently defaults to Shago service for all requests.

    Usage:
        service = ElectricityServiceRouter.resolve(VenderEnum.SHAGO)
        service.pay_electricity_bill(meter_number="12345", amount=5000)
    """

    @staticmethod
    def resolve(driver: VenderEnum) -> ElectricityServiceInterface:
        """
        Returns the appropriate electricity service based on the driver.

        Args:
            driver (VenderEnum): The vendor enum value representing the service provider.

        Returns:
            ElectricityServiceInterface: The electricity service instance for the specified vendor.
        """
        return ShagoElectricityService()