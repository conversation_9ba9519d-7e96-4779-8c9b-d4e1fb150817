from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from electricity.dtos import ElectricityPurchaseParams, ValidateMeterNumberParams


class ElectricityServiceInterface(ABC):
    @abstractmethod
    def purchase(self, params: ElectricityPurchaseParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def validate(self, params: ValidateMeterNumberParams) -> ServiceResponseData:
        pass

    # @abstractmethod
    # def get_billers(self) -> list:
    #     pass
