import logging
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from electricity.contracts import ElectricityServiceInterface
from electricity.dtos import ElectricityPurchaseParams, ValidateMeterNumberParams
from electricity.enums import ElectricityBiller
from integration.shago.client import ShagoClient
from integration.shago.dto import ElectricityPurchasePayload, ValidateMeterPayload

logger = logging.getLogger(__name__)

class ShagoElectricityService(ElectricityServiceInterface):

    def __init__(self):
        self.client = ShagoClient()

    def purchase(self, params: ElectricityPurchaseParams) -> ServiceResponseData:
        try:
            payload = self.__resolve_electricity_purchase_payload(params)
            _, response = self.client.purchase_electricity(payload)
            
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            if response.status == "400":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message,
                data=response.to_dict(),
            )
        except Exception as e:
            logger.exception(
                f"Electricity purchase failed for reference {params.reference}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )

    def validate(self, params: ValidateMeterNumberParams) -> ServiceResponseData:
        try:
            disco, meter_type = self.__resolve_disco_and_meter_type(params.biller)
            payload = ValidateMeterPayload(
                disco=disco,
                meterNo=params.meter_number,
                type=meter_type
            )
            _, response = self.client.validate_meter(
                payload
            )
            
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message="Meter validation successful",
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Meter validation failed",
                data=response.to_dict(),
            )
        except Exception as e:
            logger.exception(
                f"Meter validation failed for meter {params.meter_number}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )
        
    #TODO: Uncomment this when we have electricity billers from shago
    # def get_billers(self) -> list:
    #     try:
    #         status_code, response = self.client.fetch_electricity_billers()
    #         if status_code == 200 and response:
    #             return response
    #         return []
    #     except Exception as e:
    #         logger.exception(f"Failed to fetch electricity billers: {str(e)}")
    #         return []

    def __resolve_electricity_purchase_payload(self, params: ElectricityPurchaseParams) -> ElectricityPurchasePayload:
        disco, meter_type = self.__resolve_disco_and_meter_type(params.biller)
        return ElectricityPurchasePayload(
            disco=disco,
            meterNo=params.meter_number,
            type=meter_type,
            amount=params.amount,
            phonenumber=params.phone,
            name=params.email.split('@')[0],  # Using part of email as name #TODO: ask Mojeed for better way(if any)
            address="N/A",  # Default address
            request_id=params.reference,
        )

    def __resolve_disco_and_meter_type(self, biller: ElectricityBiller) -> tuple:
        
        biller_mapping = {
            ElectricityBiller.IkejaPrepaid: ("IKEDC", "PREPAID"),
            ElectricityBiller.IkejaPostpaid: ("IKEDC", "POSTPAID"),
            ElectricityBiller.EkoPrepaid: ("EKEDC", "PREPAID"),
            ElectricityBiller.EkoPostpaid: ("EKEDC", "POSTPAID"),
            ElectricityBiller.AbujaPrepaid: ("AEDC", "PREPAID"),
            ElectricityBiller.AbujaPostpaid: ("AEDC", "POSTPAID"),
            ElectricityBiller.BeninPrepaid: ("IBEDC", "PREPAID"),
            ElectricityBiller.BeninPostpaid: ("IBEDC", "POSTPAID"),
            ElectricityBiller.EnuguPrepaid: ("NEDC", "PREPAID"),
            ElectricityBiller.EnuguPostpaid: ("NEDC", "POSTPAID"),
            ElectricityBiller.IbadanPrepaid: ("IBEDC", "PREPAID"),
            ElectricityBiller.IbadanPostPaid: ("IBEDC", "POSTPAID"),
            ElectricityBiller.JosPrepaid: ("JEDC", "PREPAID"),
            ElectricityBiller.JosPostpaid: ("JEDC", "POSTPAID"),
            ElectricityBiller.KadunaPrepaid: ("KEDC", "PREPAID"),
            ElectricityBiller.KadunaPostpaid: ("KEDC", "POSTPAID"),
            ElectricityBiller.KanoPrepaid: ("KEDC", "PREPAID"),
            ElectricityBiller.KanoPostpaid: ("KEDC", "POSTPAID"),
            ElectricityBiller.PortharcourtPrepaid: ("PHEDC", "PREPAID"),
            ElectricityBiller.PortharcourtPostpaid: ("PHEDC", "POSTPAID"),
            ElectricityBiller.YolaPrepaid: ("YEDC", "PREPAID"),
            ElectricityBiller.YolaPostpaid: ("YEDC", "POSTPAID")
        }
        
        # Default to IKEDC and PREPAID if mapping not found
        return biller_mapping.get(biller, ("IKEDC", "PREPAID"))
