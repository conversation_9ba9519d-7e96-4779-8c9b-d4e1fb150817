from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from data.dtos import DataPurchaseParams
from data.enums import DataNetworkEnum


class DataServiceInterface(ABC):
    @abstractmethod
    def purchase(self, params: DataPurchaseParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def lookup(self, network: DataNetworkEnum) -> ServiceResponseData:
        pass
