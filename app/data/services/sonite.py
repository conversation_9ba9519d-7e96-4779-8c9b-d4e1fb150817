import logging

from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from data.contracts import DataServiceInterface
from data.dtos import DataPurchaseParams
from data.enums import DataNetworkEnum
from integration.sonite.client import SoniteClient
from integration.sonite.dto import VendDataPayload

logger = logging.getLogger(__name__)


class SoniteDataService(DataServiceInterface):
    def __init__(self):
        self.client = SoniteClient()

    def purchase(self, params: DataPurchaseParams) -> ServiceResponseData:
        payload = self.__resolve_vend_data_payload(params)
        try:
            _, response = self.client.vend_data(payload)
            if response.responseCode == "00":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.responseMessage,
                    data=response.to_dict(),
                )
            if response.responseCode == "09" or response.responseCode == "XX":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.responseMessage,
                    data=response.to_dict(),
                )
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.responseMessage,
                data=response.to_dict(),
            )
        except Exception as e:
            logger.exception(
                f"Data Purchase failed for reference {params.reference}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Pending.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )

    def __resolve_vend_data_payload(
        self, params: DataPurchaseParams
    ) -> VendDataPayload:
        return VendDataPayload(
            data_code=params.network,  # TODO: Fetch from Database Mapping
            product_code=self.__resolve_product_code(params.network),
            phone_number=params.phone,
            marchant_ref=params.reference,
        )

    @staticmethod
    def __resolve_product_code(network: DataNetworkEnum) -> str:
        match network:
            case DataNetworkEnum.Mtn:
                return "MTNDATA"
            case DataNetworkEnum.Airtel:
                return "AIRTELDATA"
            case DataNetworkEnum.Glo:
                return "GLODATA"
            case DataNetworkEnum.Etisalat:
                return "9MOBILEDATA"

    def lookup(self, network: DataNetworkEnum) -> ServiceResponseData:
        try:
            resolved_network = self.__resolve_product_code(network)
            _, response = self.client.data_bundles(resolved_network)
            if response.responseCode == "00":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.responseMessage,
                    data=response.to_dict(),
                )

            if response.responseCode == "09" or response.responseCode == "XX":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.responseMessage,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.responseMessage,
                data=response.to_dict(),
            )
        except Exception as e:
            logger.exception(f"Data Lookup failed for network {network}: {str(e)}")
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )
