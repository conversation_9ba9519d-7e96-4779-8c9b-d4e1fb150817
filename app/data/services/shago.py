import logging

from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from data.contracts import DataServiceInterface
from data.dtos import DataPurchaseParams
from data.enums import DataNetworkEnum
from integration.shago.client import ShagoClient
from integration.shago.dto import DataVendingPayload

logger = logging.getLogger(__name__)


class ShagoDataService(DataServiceInterface):
    def __init__(self):
        self.client = ShagoClient()

    def purchase(self, params: DataPurchaseParams) -> ServiceResponseData:
        try:
            payload = self.__resolve_data_purchase_payload(params)
            _, response = self.client.data_vending(payload)
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            if response.status == "400":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message,
                data=response.to_dict(),
            )

        except Exception as e:
            logger.exception(
                f"Data Purchase failed for reference {params.reference}: {str(e)}"
            )
            return ServiceResponseData(
                status=ServiceResponseStatus.Pending.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )

    def __resolve_data_purchase_payload(
        params: DataPurchaseParams,
    ) -> DataVendingPayload:
        return DataVendingPayload(
            phone=params.phone,
            amount=params.amount,
            bundle=params.data_code,
            network=params.network,
            package=params.data_code,
            request_id=params.reference,
        )

    def lookup(self, network: DataNetworkEnum) -> ServiceResponseData:
        try:
            _, response = self.client.get_data_bundle(network)
            if response.status == "200":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Success.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            if response.status == "400":
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message,
                    data=response.to_dict(),
                )

            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=response.message,
                data=response.to_dict(),
            )

        except Exception as e:
            logger.exception(f"Data Lookup failed for network {network}: {str(e)}")
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=GENERIC_EXCEPTION_ERROR_MESSAGE,
                data=None,
            )
