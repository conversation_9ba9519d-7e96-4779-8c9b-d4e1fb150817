from education.contracts import EducationServiceInterface
from education.services.shago import ShagoEducationService
from common.enums import VenderEnum


class EducationServiceRouter:
    """
    Routes education service requests to the appropriate vendor-specific implementation.

    This class acts as a dynamic service router that delegates education-related method calls
    (e.g., verify, purchase) to the correct service implementation (such as Shago)
    based on the provided `driver` (vendor). This abstraction allows the business logic to interact
    with a unified interface while enabling easy switching between different education service providers.
    """

    @staticmethod
    def resolve(driver: VenderEnum) -> EducationServiceInterface:
        """
        Returns the appropriate education service based on the driver.

        Args:
            driver (VenderEnum): The vendor to use for the education service.

        Returns:
            EducationServiceInterface: The education service instance.
        """
        if driver == VenderEnum.Shago:
            return ShagoEducationService()
        else:
            # Default to Shago for now, can add other vendors later
            return ShagoEducationService()
