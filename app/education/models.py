from django.db import models

from common.models import AuditableModel
from education.enums import EducationProviderEnum, EducationServiceTypeEnum


class CentralEducationPlan(AuditableModel):
    """Central education service plans."""
    provider = models.CharField(
        max_length=50, 
        choices=EducationProviderEnum.choices(), 
        db_index=True
    )
    service_type = models.CharField(
        max_length=50, 
        choices=EducationServiceTypeEnum.choices(), 
        db_index=True
    )
    code = models.CharField(max_length=100, db_index=True)
    name = models.CharField(max_length=150)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    objects = models.Manager()

    def __str__(self):
        return f"{self.name} ({self.provider} - {self.service_type})"

    class Meta:
        ordering = ("provider", "service_type")
        unique_together = ("provider", "service_type", "code")


class ShagoEducationPlan(AuditableModel):
    """Shago-specific education service plans."""
    provider = models.CharField(
        max_length=50, 
        choices=EducationProviderEnum.choices(), 
        db_index=True
    )
    central_plan = models.OneToOneField(
        "education.CentralEducationPlan", 
        on_delete=models.PROTECT, 
        related_name="shago_education_plan"
    )
    code = models.CharField(max_length=100, db_index=True)
    name = models.CharField(max_length=150)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    service_type = models.CharField(
        max_length=50, 
        choices=EducationServiceTypeEnum.choices()
    )
    is_active = models.BooleanField(default=True)

    objects = models.Manager()

    def __str__(self):
        return f"{self.name} ({self.provider} - Shago)"

    class Meta:
        ordering = ("provider", "service_type")
        unique_together = ("provider", "service_type", "code")
