from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from education.dtos import (
    WaecLookupParams, WaecPurchaseParams,
    JambOptionsParams, JambValidateParams, JambPurchaseParams
)


class EducationServiceInterface(ABC):
    """Interface for education service implementations."""

    # WAEC methods
    @abstractmethod
    def waec_lookup(self, params: WaecLookupParams) -> ServiceResponseData:
        """Get WAEC pricing and availability."""
        pass

    @abstractmethod
    def waec_purchase(self, params: WaecPurchaseParams) -> ServiceResponseData:
        """Purchase WAEC result checker pins."""
        pass

    # JAMB methods
    @abstractmethod
    def jamb_options(self, params: JambOptionsParams) -> ServiceResponseData:
        """Get JAMB pricing and options."""
        pass

    @abstractmethod
    def jamb_validate(self, params: JambValidateParams) -> ServiceResponseData:
        """Validate JAMB profile code."""
        pass

    @abstractmethod
    def jamb_purchase(self, params: JambPurchaseParams) -> ServiceResponseData:
        """Purchase JAMB pin."""
        pass
