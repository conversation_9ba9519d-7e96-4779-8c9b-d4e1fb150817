from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from education.dtos import EducationVerifyParams, EducationPurchaseParams


class EducationServiceInterface(ABC):
    """Interface for education service implementations."""

    @abstractmethod
    def verify(self, params: EducationVerifyParams) -> ServiceResponseData:
        """Verify education credentials (exam number, year, etc.)."""
        pass

    @abstractmethod
    def purchase(self, params: EducationPurchaseParams) -> ServiceResponseData:
        """Purchase education services (result checker pins, forms, etc.)."""
        pass

    @abstractmethod
    def get_service_plans(self, provider: str, service_type: str) -> ServiceResponseData:
        """Get available service plans for a provider and service type."""
        pass
