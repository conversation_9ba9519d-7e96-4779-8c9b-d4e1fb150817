from dataclasses import dataclass
from typing import Optional, List

from common.utils import ToDict
from education.enums import EducationProviderEnum, EducationServiceTypeEnum, WaecExamTypeEnum, JambExamTypeEnum


# WAEC DTOs
@dataclass
class WaecLookupParams(ToDict):
    """Parameters for WAEC lookup (no parameters needed)."""
    pass


@dataclass
class WaecPurchaseParams(ToDict):
    """Parameters for purchasing WAEC pins."""
    number_of_pins: int
    amount: float
    reference: str


# JAMB DTOs
@dataclass
class JambOptionsParams(ToDict):
    """Parameters for JAMB options lookup (no parameters needed)."""
    pass


@dataclass
class JambValidateParams(ToDict):
    """Parameters for JAMB profile validation."""
    exam_type: str  # UTME or DE
    profile_code: str


@dataclass
class JambPurchaseParams(ToDict):
    """Parameters for purchasing JAMB pins."""
    exam_type: str  # UTME or DE
    profile_code: str
    amount: int
    reference: str


# Response DTOs
@dataclass
class WaecProduct(ToDict):
    """WAEC product information."""
    price: Optional[int] = None
    available_count: Optional[int] = None


@dataclass
class WaecLookupResponseDetails(ToDict):
    """WAEC lookup response details."""
    message: Optional[str] = None
    status: Optional[str] = None
    type: Optional[str] = None
    products: Optional[List[WaecProduct]] = None


@dataclass
class WaecPin(ToDict):
    """WAEC pin details."""
    pin: Optional[str] = None
    serial: Optional[str] = None
    expiry_date: Optional[str] = None


@dataclass
class WaecPurchaseResponseDetails(ToDict):
    """WAEC purchase response details."""
    amount: Optional[int] = None
    message: Optional[str] = None
    status: Optional[str] = None
    transaction_id: Optional[str] = None
    date: Optional[str] = None
    pins: Optional[List[WaecPin]] = None


@dataclass
class JambProduct(ToDict):
    """JAMB product information."""
    price: Optional[int] = None
    type: Optional[str] = None


@dataclass
class JambOptionsResponseDetails(ToDict):
    """JAMB options response details."""
    message: Optional[str] = None
    status: Optional[str] = None
    products: Optional[List[JambProduct]] = None
    charge: Optional[int] = None
    type: Optional[str] = None


@dataclass
class JambValidateResponseDetails(ToDict):
    """JAMB validation response details."""
    first_name: Optional[str] = None
    surname: Optional[str] = None
    middle_name: Optional[str] = None
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    exam_type: Optional[str] = None
    profile_code: Optional[str] = None
    info: Optional[str] = None
    message: Optional[str] = None
    status: Optional[bool] = None


@dataclass
class JambPurchaseResponseDetails(ToDict):
    """JAMB purchase response details."""
    amount: Optional[str] = None
    fee: Optional[str] = None
    message: Optional[str] = None
    status: Optional[str] = None
    surname: Optional[str] = None
    first_name: Optional[str] = None
    middle_name: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    pin: Optional[int] = None
    profile_code: Optional[str] = None
    date: Optional[str] = None
    transaction_id: Optional[str] = None
    product: Optional[str] = None
    exam_type: Optional[str] = None


# @dataclass(init=False)
# class JambPurchaseResponse(ToDict, FieldMapper):
#     amount: Optional[str] = None
#     fee: Optional[str] = None
#     message: Optional[str] = "No response from service provider"
#     status: Optional[str] = "pending"
#     surname: Optional[str] = None
#     firstName: Optional[str] = None
#     middleName: Optional[str] = None
#     fullName: Optional[str] = None
#     phone: Optional[str] = None
#     pin: Optional[int] = None
#     profileCode: Optional[str] = None
#     date: Optional[str] = None
#     transId: Optional[str] = None
#     product: Optional[str] = None
#     type: Optional[str] = None
