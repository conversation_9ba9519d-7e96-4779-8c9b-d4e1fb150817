from dataclasses import dataclass
from typing import Optional

from common.utils import ToDict
from education.enums import EducationProviderEnum, EducationServiceTypeEnum, WaecExamTypeEnum, JambExamTypeEnum


@dataclass
class EducationVerifyParams(ToDict):
    """Parameters for verifying education credentials."""
    provider: EducationProviderEnum
    service_type: EducationServiceTypeEnum
    exam_number: str
    exam_year: str
    exam_type: Optional[str] = None  # WASSCE, GCE for WAEC; UTME, DE for JAMB


@dataclass
class EducationPurchaseParams(ToDict):
    """Parameters for purchasing education services."""
    provider: EducationProviderEnum
    service_type: EducationServiceTypeEnum
    phone: str
    email: str
    reference: str
    amount: float
    exam_year: Optional[str] = None
    exam_type: Optional[str] = None
    quantity: Optional[int] = 1


@dataclass
class EducationResponseDetails(ToDict):
    """Response details for education services."""
    provider: str
    service_type: str
    exam_number: Optional[str] = None
    exam_year: Optional[str] = None
    candidate_name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    amount: Optional[float] = None
    pin: Optional[str] = None
    serial_number: Optional[str] = None
    transaction_id: Optional[str] = None
