from common.enums import CustomEnum


class EducationProviderEnum(CustomEnum):
    Waec = "Waec"
    Jamb = "Jamb"


class EducationServiceTypeEnum(CustomEnum):
    WAEC_RESULT_CHECKER = "WAEC_RESULT_CHECKER"
    JAMB_RESULT_CHECKER = "JAMB_RESULT_CHECKER"
    JAMB_FORM = "JAMB_FORM"


class WaecExamTypeEnum(CustomEnum):
    WASSCE = "WASSCE"
    GCE = "GCE"


class JambExamTypeEnum(CustomEnum):
    UTME = "UTME"
    DE = "DE"
