from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from pykolofinance.common.serializers import EmptySerializer

from education.v1.serializers import (
    EducationVerifySerializer,
    EducationPurchaseSerializer,
    EducationServicePlansSerializer
)
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus


@extend_schema_view(
    verify=header,
    purchase=header,
    service_plans=header,
)
class EducationViewSet(viewsets.GenericViewSet):
    """ViewSet for education services."""
    permission_classes = [HasAPIKey]

    @action(
        detail=False,
        methods=["post"],
        serializer_class=EducationVerifySerializer,
        url_path="verify",
    )
    def verify(self, request):
        """
        Verify education credentials (exam number, year, etc.).
        
        This endpoint allows verification of education credentials such as
        WAEC or JAMB exam numbers and years.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                response_data = serializer.verify()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"Verification failed: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request data",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(
        detail=False,
        methods=["post"],
        serializer_class=EducationPurchaseSerializer,
        url_path="purchase",
    )
    def purchase(self, request):
        """
        Purchase education services (result checker pins, forms, etc.).
        
        This endpoint allows purchasing of education services such as
        WAEC result checker pins or JAMB forms.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                response_data = serializer.purchase()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"Purchase failed: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request data",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(
        detail=False,
        methods=["get"],
        serializer_class=EducationServicePlansSerializer,
        url_path="service-plans",
    )
    def service_plans(self, request):
        """
        Get available education service plans.
        
        This endpoint returns available service plans for a specific
        education provider and service type.
        """
        serializer = self.get_serializer(data=request.query_params)
        if serializer.is_valid():
            try:
                response_data = serializer.get_plans()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"Failed to fetch service plans: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request parameters",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )
