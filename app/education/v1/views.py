from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAP<PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from pykolofinance.common.serializers import EmptySerializer

from education.v1.serializers import (
    WaecLookupSerializer,
    WaecPurchaseSerializer,
    JambOptionsSerializer,
    JambValidateSerializer,
    JambPurchaseSerializer
)
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus


@extend_schema_view(
    waec_lookup=header,
    waec_purchase=header,
    jamb_options=header,
    jamb_validate=header,
    jamb_purchase=header,
)
class EducationViewSet(viewsets.GenericViewSet):
    """ViewSet for education services."""
    permission_classes = [HasAPIKey]

    # WAEC endpoints
    @action(
        detail=False,
        methods=["get"],
        serializer_class=WaecLookupSerializer,
        url_path="waec/lookup",
    )
    def waec_lookup(self, request):
        """
        Get WAEC pricing and availability.

        This endpoint returns WAEC result checker pricing and availability.
        """
        serializer = self.get_serializer()
        try:
            response_data = serializer.lookup()
            return Response(
                response_data.to_dict(),
                status=response_data.status_code
            )
        except Exception as e:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=f"WAEC lookup failed: {str(e)}",
                data=None,
            )
            return Response(
                error_response.to_dict(),
                status=error_response.status_code
            )

    @action(
        detail=False,
        methods=["post"],
        serializer_class=WaecPurchaseSerializer,
        url_path="waec/purchase",
    )
    def waec_purchase(self, request):
        """
        Purchase WAEC result checker pins.

        This endpoint allows purchasing of WAEC result checker pins.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                response_data = serializer.purchase()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"WAEC purchase failed: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request data",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )

    # JAMB endpoints
    @action(
        detail=False,
        methods=["get"],
        serializer_class=JambOptionsSerializer,
        url_path="jamb/options",
    )
    def jamb_options(self, request):
        """
        Get JAMB pricing and options.

        This endpoint returns JAMB pricing and available options.
        """
        serializer = self.get_serializer()
        try:
            response_data = serializer.get_options()
            return Response(
                response_data.to_dict(),
                status=response_data.status_code
            )
        except Exception as e:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message=f"JAMB options retrieval failed: {str(e)}",
                data=None,
            )
            return Response(
                error_response.to_dict(),
                status=error_response.status_code
            )

    @action(
        detail=False,
        methods=["post"],
        serializer_class=JambValidateSerializer,
        url_path="jamb/validate",
    )
    def jamb_validate(self, request):
        """
        Validate JAMB profile code.

        This endpoint validates a JAMB profile code and returns candidate information.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                response_data = serializer.validate()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"JAMB validation failed: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request data",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(
        detail=False,
        methods=["post"],
        serializer_class=JambPurchaseSerializer,
        url_path="jamb/purchase",
    )
    def jamb_purchase(self, request):
        """
        Purchase JAMB pin.

        This endpoint allows purchasing of JAMB pins.
        """
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                response_data = serializer.purchase()
                return Response(
                    response_data.to_dict(),
                    status=response_data.status_code
                )
            except Exception as e:
                error_response = ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=f"JAMB purchase failed: {str(e)}",
                    data=None,
                )
                return Response(
                    error_response.to_dict(),
                    status=error_response.status_code
                )
        else:
            error_response = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Invalid request data",
                data=serializer.errors,
            )
            return Response(
                error_response.to_dict(),
                status=status.HTTP_400_BAD_REQUEST
            )
