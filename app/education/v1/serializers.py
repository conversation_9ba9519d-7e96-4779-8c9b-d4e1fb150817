from rest_framework import serializers

from education.dtos import (
    WaecLookupParams, WaecPurchaseParams,
    JambOptionsParams, JambValidateParams, JambPurchaseParams
)
from education.enums import EducationProviderEnum, JambExamTypeEnum
from education.service_router import EducationServiceRouter
from common.enums import VenderEnum, ProductEnum, ProviderEnum
from provider.utils import get_vender


# WAEC Serializers
class WaecLookupSerializer(serializers.Serializer):
    """Serializer for WAEC lookup requests."""

    def lookup(self):
        """Perform WAEC lookup."""
        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Waec)

        # Create lookup parameters
        params = WaecLookupParams()

        # Route to appropriate service and lookup
        service = EducationServiceRouter.resolve(vender)
        return service.waec_lookup(params)


class WaecPurchaseSerializer(serializers.Serializer):
    """Serializer for WAEC purchase requests."""
    number_of_pins = serializers.IntegerField(min_value=1, max_value=10)
    amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    reference = serializers.CharField(max_length=100)

    def purchase(self):
        """Perform WAEC purchase."""
        validated_data = self.validated_data

        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Waec)

        # Create purchase parameters
        params = WaecPurchaseParams(
            number_of_pins=validated_data['number_of_pins'],
            amount=float(validated_data['amount']),
            reference=validated_data['reference'],
        )

        # Route to appropriate service and purchase
        service = EducationServiceRouter.resolve(vender)
        return service.waec_purchase(params)


# JAMB Serializers
class JambOptionsSerializer(serializers.Serializer):
    """Serializer for JAMB options requests."""

    def get_options(self):
        """Get JAMB options."""
        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Jamb)

        # Create options parameters
        params = JambOptionsParams()

        # Route to appropriate service and get options
        service = EducationServiceRouter.resolve(vender)
        return service.jamb_options(params)


class JambValidateSerializer(serializers.Serializer):
    """Serializer for JAMB validation requests."""
    exam_type = serializers.ChoiceField(choices=JambExamTypeEnum.choices())
    profile_code = serializers.CharField(max_length=50)

    def validate(self):
        """Perform JAMB validation."""
        validated_data = self.validated_data

        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Jamb)

        # Create validation parameters
        params = JambValidateParams(
            exam_type=validated_data['exam_type'],
            profile_code=validated_data['profile_code'],
        )

        # Route to appropriate service and validate
        service = EducationServiceRouter.resolve(vender)
        return service.jamb_validate(params)


class JambPurchaseSerializer(serializers.Serializer):
    """Serializer for JAMB purchase requests."""
    exam_type = serializers.ChoiceField(choices=JambExamTypeEnum.choices())
    profile_code = serializers.CharField(max_length=50)
    amount = serializers.IntegerField(min_value=1)
    reference = serializers.CharField(max_length=100)

    def purchase(self):
        """Perform JAMB purchase."""
        validated_data = self.validated_data

        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Jamb)

        # Create purchase parameters
        params = JambPurchaseParams(
            exam_type=validated_data['exam_type'],
            profile_code=validated_data['profile_code'],
            amount=validated_data['amount'],
            reference=validated_data['reference'],
        )

        # Route to appropriate service and purchase
        service = EducationServiceRouter.resolve(vender)
        return service.jamb_purchase(params)
