from rest_framework import serializers

from education.dtos import EducationVerifyParams, EducationPurchaseParams
from education.enums import EducationProviderEnum, EducationServiceTypeEnum, WaecExamTypeEnum, JambExamTypeEnum
from education.service_router import EducationServiceRouter
from common.enums import VenderEnum, ProductEnum, ProviderEnum
from provider.utils import get_vender


class EducationVerifySerializer(serializers.Serializer):
    """Serializer for education verification requests."""
    provider = serializers.ChoiceField(choices=EducationProviderEnum.choices())
    service_type = serializers.ChoiceField(choices=EducationServiceTypeEnum.choices())
    exam_number = serializers.CharField(max_length=50)
    exam_year = serializers.CharField(max_length=4)
    exam_type = serializers.CharField(max_length=20, required=False, allow_blank=True)

    def validate(self, attrs):
        """Validate the education verification data."""
        provider = attrs.get('provider')
        service_type = attrs.get('service_type')
        exam_type = attrs.get('exam_type')

        # Validate exam type based on provider
        if provider == EducationProviderEnum.Waec.value:
            if exam_type and exam_type not in WaecExamTypeEnum.values():
                raise serializers.ValidationError({
                    'exam_type': f'Invalid exam type for WAEC. Valid options: {WaecExamTypeEnum.values()}'
                })
        elif provider == EducationProviderEnum.Jamb.value:
            if exam_type and exam_type not in JambExamTypeEnum.values():
                raise serializers.ValidationError({
                    'exam_type': f'Invalid exam type for JAMB. Valid options: {JambExamTypeEnum.values()}'
                })

        return attrs

    def verify(self):
        """Perform the education verification."""
        validated_data = self.validated_data
        
        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Waec)  # Default to WAEC for now
        
        # Create verification parameters
        params = EducationVerifyParams(
            provider=EducationProviderEnum(validated_data['provider']),
            service_type=EducationServiceTypeEnum(validated_data['service_type']),
            exam_number=validated_data['exam_number'],
            exam_year=validated_data['exam_year'],
            exam_type=validated_data.get('exam_type'),
        )
        
        # Route to appropriate service and verify
        service = EducationServiceRouter.resolve(vender)
        return service.verify(params)


class EducationPurchaseSerializer(serializers.Serializer):
    """Serializer for education service purchase requests."""
    provider = serializers.ChoiceField(choices=EducationProviderEnum.choices())
    service_type = serializers.ChoiceField(choices=EducationServiceTypeEnum.choices())
    phone = serializers.CharField(max_length=15)
    email = serializers.EmailField()
    reference = serializers.CharField(max_length=100)
    amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    exam_year = serializers.CharField(max_length=4, required=False, allow_blank=True)
    exam_type = serializers.CharField(max_length=20, required=False, allow_blank=True)
    quantity = serializers.IntegerField(default=1, min_value=1, max_value=10)

    def validate(self, attrs):
        """Validate the education purchase data."""
        provider = attrs.get('provider')
        service_type = attrs.get('service_type')
        exam_type = attrs.get('exam_type')

        # Validate exam type based on provider
        if provider == EducationProviderEnum.Waec.value:
            if exam_type and exam_type not in WaecExamTypeEnum.values():
                raise serializers.ValidationError({
                    'exam_type': f'Invalid exam type for WAEC. Valid options: {WaecExamTypeEnum.values()}'
                })
        elif provider == EducationProviderEnum.Jamb.value:
            if exam_type and exam_type not in JambExamTypeEnum.values():
                raise serializers.ValidationError({
                    'exam_type': f'Invalid exam type for JAMB. Valid options: {JambExamTypeEnum.values()}'
                })

        return attrs

    def purchase(self):
        """Perform the education service purchase."""
        validated_data = self.validated_data
        
        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Waec)  # Default to WAEC for now
        
        # Create purchase parameters
        params = EducationPurchaseParams(
            provider=EducationProviderEnum(validated_data['provider']),
            service_type=EducationServiceTypeEnum(validated_data['service_type']),
            phone=validated_data['phone'],
            email=validated_data['email'],
            reference=validated_data['reference'],
            amount=float(validated_data['amount']),
            exam_year=validated_data.get('exam_year'),
            exam_type=validated_data.get('exam_type'),
            quantity=validated_data.get('quantity', 1),
        )
        
        # Route to appropriate service and purchase
        service = EducationServiceRouter.resolve(vender)
        return service.purchase(params)


class EducationServicePlansSerializer(serializers.Serializer):
    """Serializer for fetching education service plans."""
    provider = serializers.ChoiceField(choices=EducationProviderEnum.choices())
    service_type = serializers.ChoiceField(choices=EducationServiceTypeEnum.choices())

    def get_plans(self):
        """Get available service plans."""
        validated_data = self.validated_data
        
        # Get the appropriate vendor for education services
        vender = get_vender(ProductEnum.Education, ProviderEnum.Waec)  # Default to WAEC for now
        
        # Route to appropriate service and get plans
        service = EducationServiceRouter.resolve(vender)
        return service.get_service_plans(
            provider=validated_data['provider'],
            service_type=validated_data['service_type']
        )
