import logging
from typing import Dict

from education.dtos import EducationVerifyParams, EducationPurchaseParams, EducationResponseDetails
from education.services.base import BaseEducationService
from education.models import ShagoEducationPlan
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.shago.client import ShagoClient
from integration.shago.dto import EducationVerifyPayload, EducationPurchasePayload, EducationVerifyResponse, EducationPurchaseResponse

logger = logging.getLogger(__name__)


class ShagoEducationService(BaseEducationService):
    """Shago implementation of the education service interface."""

    STATUS_MAPPINGS = {
        "200": ServiceResponseStatus.Success,
        "400": ServiceResponseStatus.Pending,
    }

    def __init__(self):
        """Initialize the Shago education client."""
        super().__init__(ShagoClient())

    def verify(self, params: EducationVerifyParams) -> ServiceResponseData:
        """Verify education credentials (exam number, year, etc.)."""
        try:
            payload = self._create_verify_payload(params)
            _, response = self.client.education_verify(payload)
            
            status = self._map_status(response.status)
            response_details = self._extract_verify_response_details(response, params)
            
            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "Verification successful"
                )
            elif status == ServiceResponseStatus.Pending:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message or "Verification in progress",
                    data=response_details.to_dict(),
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "Verification failed",
                    data=response_details.to_dict(),
                )
                
        except Exception as e:
            logger.exception(f"Education verification failed for {params.provider.value}: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def purchase(self, params: EducationPurchaseParams) -> ServiceResponseData:
        """Purchase education services (result checker pins, forms, etc.)."""
        try:
            payload = self._create_purchase_payload(params)
            _, response = self.client.education_purchase(payload)
            
            status = self._map_status(response.status)
            response_details = self._extract_purchase_response_details(response, params)
            
            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "Purchase successful"
                )
            elif status == ServiceResponseStatus.Pending:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message or "Purchase in progress",
                    data=response_details.to_dict(),
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "Purchase failed",
                    data=response_details.to_dict(),
                )
                
        except Exception as e:
            logger.exception(f"Education purchase failed for {params.provider.value}: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def get_service_plans(self, provider: str, service_type: str) -> ServiceResponseData:
        """Get available service plans for a provider and service type."""
        try:
            plans = ShagoEducationPlan.objects.filter(
                provider=provider,
                service_type=service_type,
                is_active=True
            ).values('code', 'name', 'price', 'service_type')
            
            return self._create_success_response(
                data=list(plans),
                message="Service plans retrieved successfully"
            )
            
        except Exception as e:
            logger.exception(f"Failed to fetch education plans for {provider}-{service_type}: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def _create_verify_payload(self, params: EducationVerifyParams) -> EducationVerifyPayload:
        """Create verification payload for Shago API."""
        return EducationVerifyPayload(
            provider=params.provider.value,
            service_type=params.service_type.value,
            exam_number=params.exam_number,
            exam_year=params.exam_year,
            exam_type=params.exam_type,
        )

    def _create_purchase_payload(self, params: EducationPurchaseParams) -> EducationPurchasePayload:
        """Create purchase payload for Shago API."""
        return EducationPurchasePayload(
            provider=params.provider.value,
            service_type=params.service_type.value,
            phone=params.phone,
            email=params.email,
            amount=params.amount,
            quantity=params.quantity or 1,
            request_id=params.reference,
            exam_year=params.exam_year,
            exam_type=params.exam_type,
        )

    def _extract_verify_response_details(self, response: EducationVerifyResponse, params: EducationVerifyParams) -> EducationResponseDetails:
        """Extract verification response details."""
        return EducationResponseDetails(
            provider=params.provider.value,
            service_type=params.service_type.value,
            exam_number=response.exam_number or params.exam_number,
            exam_year=response.exam_year or params.exam_year,
            candidate_name=response.candidate_name,
        )

    def _extract_purchase_response_details(self, response: EducationPurchaseResponse, params: EducationPurchaseParams) -> EducationResponseDetails:
        """Extract purchase response details."""
        return EducationResponseDetails(
            provider=params.provider.value,
            service_type=params.service_type.value,
            phone=response.phone or params.phone,
            email=response.email or params.email,
            amount=float(response.amount) if response.amount else params.amount,
            pin=response.pin,
            serial_number=response.serial_number,
            transaction_id=response.transId,
        )
