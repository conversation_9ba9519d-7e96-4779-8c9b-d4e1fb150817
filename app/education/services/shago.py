import logging
from typing import Dict

from education.dtos import (
    WaecLookupParams, WaecPurchaseParams, WaecLookupResponseDetails, WaecPurchaseResponseDetails,
    JambOptionsParams, JambValidateParams, JambPurchaseParams,
    JambOptionsResponseDetails, JambValidateResponseDetails, JambPurchaseResponseDetails,
    WaecProduct, WaecPin, JambProduct
)
from education.services.base import BaseEducationService
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.shago.client import ShagoClient
from integration.shago.dto import (
    WaecPurchasePayload, JambValidatePayload, JambPurchasePayload,
    WaecLookupResponse, WaecPurchaseResponse,
    JambOptionsResponse, JambValidateResponse, JambPurchaseResponse
)

logger = logging.getLogger(__name__)


class ShagoEducationService(BaseEducationService):
    """Shago implementation of the education service interface."""

    STATUS_MAPPINGS = {
        "200": ServiceResponseStatus.Success,
        "400": ServiceResponseStatus.Pending,
    }

    def __init__(self):
        """Initialize the Shago education client."""
        super().__init__(ShagoClient())

    # WAEC methods
    def waec_lookup(self, params: WaecLookupParams) -> ServiceResponseData:
        """Get WAEC pricing and availability."""
        try:
            _, response = self.client.waec_lookup()

            status = self._map_status(response.status)
            response_details = self._extract_waec_lookup_response(response)

            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "WAEC lookup successful"
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "WAEC lookup failed",
                    data=response_details.to_dict(),
                )

        except Exception as e:
            logger.exception(f"WAEC lookup failed: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def waec_purchase(self, params: WaecPurchaseParams) -> ServiceResponseData:
        """Purchase WAEC result checker pins."""
        try:
            payload = self._create_waec_purchase_payload(params)
            _, response = self.client.waec_purchase(payload)

            status = self._map_status(response.status)
            response_details = self._extract_waec_purchase_response(response)

            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "WAEC purchase successful"
                )
            elif status == ServiceResponseStatus.Pending:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message or "WAEC purchase in progress",
                    data=response_details.to_dict(),
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "WAEC purchase failed",
                    data=response_details.to_dict(),
                )

        except Exception as e:
            logger.exception(f"WAEC purchase failed: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    # JAMB methods
    def jamb_options(self, params: JambOptionsParams) -> ServiceResponseData:
        """Get JAMB pricing and options."""
        try:
            _, response = self.client.jamb_options()

            status = self._map_status(response.status)
            response_details = self._extract_jamb_options_response(response)

            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "JAMB options retrieved successfully"
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "JAMB options retrieval failed",
                    data=response_details.to_dict(),
                )

        except Exception as e:
            logger.exception(f"JAMB options retrieval failed: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def jamb_validate(self, params: JambValidateParams) -> ServiceResponseData:
        """Validate JAMB profile code."""
        try:
            payload = self._create_jamb_validate_payload(params)
            _, response = self.client.jamb_validate(payload)

            # JAMB validate returns boolean status, not string
            status = ServiceResponseStatus.Success if response.status else ServiceResponseStatus.Failed
            response_details = self._extract_jamb_validate_response(response)

            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "JAMB validation successful"
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "JAMB validation failed",
                    data=response_details.to_dict(),
                )

        except Exception as e:
            logger.exception(f"JAMB validation failed: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    def jamb_purchase(self, params: JambPurchaseParams) -> ServiceResponseData:
        """Purchase JAMB pin."""
        try:
            payload = self._create_jamb_purchase_payload(params)
            _, response = self.client.jamb_purchase(payload)

            status = self._map_status(response.status)
            response_details = self._extract_jamb_purchase_response(response)

            if status == ServiceResponseStatus.Success:
                return self._create_success_response(
                    data=response_details.to_dict(),
                    message=response.message or "JAMB purchase successful"
                )
            elif status == ServiceResponseStatus.Pending:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Pending.value,
                    message=response.message or "JAMB purchase in progress",
                    data=response_details.to_dict(),
                )
            else:
                return ServiceResponseData(
                    status=ServiceResponseStatus.Failed.value,
                    message=response.message or "JAMB purchase failed",
                    data=response_details.to_dict(),
                )

        except Exception as e:
            logger.exception(f"JAMB purchase failed: {str(e)}")
            return self._create_error_response(ServiceResponseStatus.Failed.value)

    # Helper methods for creating payloads
    def _create_waec_purchase_payload(self, params: WaecPurchaseParams) -> WaecPurchasePayload:
        """Create WAEC purchase payload for Shago API."""
        return WaecPurchasePayload(
            serviceCode="WAP",
            numberOfPin=str(params.number_of_pins),
            amount=str(params.amount),
            request_id=params.reference,
        )

    def _create_jamb_validate_payload(self, params: JambValidateParams) -> JambValidatePayload:
        """Create JAMB validate payload for Shago API."""
        return JambValidatePayload(
            serviceCode="JMV",
            type=params.exam_type,
            profileCode=params.profile_code,
        )

    def _create_jamb_purchase_payload(self, params: JambPurchaseParams) -> JambPurchasePayload:
        """Create JAMB purchase payload for Shago API."""
        return JambPurchasePayload(
            serviceCode="JMB",
            type=params.exam_type,
            profileCode=params.profile_code,
            amount=params.amount,
            request_id=params.reference,
        )

    # Helper methods for extracting responses
    def _extract_waec_lookup_response(self, response: WaecLookupResponse) -> WaecLookupResponseDetails:
        """Extract WAEC lookup response details."""
        products = []
        if response.product:
            for product in response.product:
                products.append(WaecProduct(
                    price=product.price,
                    available_count=product.availableCount,
                ))

        return WaecLookupResponseDetails(
            message=response.message,
            status=response.status,
            type=response.type,
            products=products,
        )

    def _extract_waec_purchase_response(self, response: WaecPurchaseResponse) -> WaecPurchaseResponseDetails:
        """Extract WAEC purchase response details."""
        pins = []
        if response.pin:
            for pin in response.pin:
                pins.append(WaecPin(
                    pin=pin.pin,
                    serial=pin.serial,
                    expiry_date=pin.expirydat,
                ))

        return WaecPurchaseResponseDetails(
            amount=response.amount,
            message=response.message,
            status=response.status,
            transaction_id=response.transId,
            date=response.date,
            pins=pins,
        )

    def _extract_jamb_options_response(self, response: JambOptionsResponse) -> JambOptionsResponseDetails:
        """Extract JAMB options response details."""
        products = []
        if response.product:
            for product in response.product:
                products.append(JambProduct(
                    price=product.price,
                    type=product.type,
                ))

        return JambOptionsResponseDetails(
            message=response.message,
            status=response.status,
            products=products,
            charge=response.charge,
            type=response.type,
        )

    def _extract_jamb_validate_response(self, response: JambValidateResponse) -> JambValidateResponseDetails:
        """Extract JAMB validate response details."""
        return JambValidateResponseDetails(
            first_name=response.firstName,
            surname=response.surname,
            middle_name=response.middleName,
            full_name=response.fullName,
            phone_number=response.phoneNumber,
            exam_type=response.type,
            profile_code=response.profileCode,
            info=response.info,
            message=response.message,
            status=response.status,
        )

    def _extract_jamb_purchase_response(self, response: JambPurchaseResponse) -> JambPurchaseResponseDetails:
        """Extract JAMB purchase response details."""
        return JambPurchaseResponseDetails(
            amount=response.amount,
            fee=response.fee,
            message=response.message,
            status=response.status,
            surname=response.surname,
            first_name=response.firstName,
            middle_name=response.middleName,
            full_name=response.fullName,
            phone=response.phone,
            pin=response.pin,
            profile_code=response.profileCode,
            date=response.date,
            transaction_id=response.transId,
            product=response.product,
            exam_type=response.type,
        )
