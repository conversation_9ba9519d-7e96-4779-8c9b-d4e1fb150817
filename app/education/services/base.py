import logging
from abc import ABC
from typing import Dict

from education.contracts import EducationServiceInterface
from education.dtos import EducationVerifyParams, EducationPurchaseParams, EducationResponseDetails
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus

logger = logging.getLogger(__name__)


class BaseEducationService(EducationServiceInterface, ABC):
    """Base class for education services with common functionality."""

    STATUS_MAPPINGS: Dict[str, ServiceResponseStatus] = {}

    def __init__(self, client):
        self.client = client

    def _create_error_response(self, status: str, message: str = None) -> ServiceResponseData:
        """Create a standardized error response."""
        return ServiceResponseData(
            status=status,
            message=message or GENERIC_EXCEPTION_ERROR_MESSAGE,
            data=None,
        )

    def _create_success_response(self, data: dict, message: str = None) -> ServiceResponseData:
        """Create a standardized success response."""
        return ServiceResponseData(
            status=ServiceResponseStatus.Success.value,
            message=message or "Operation completed successfully",
            data=data,
        )

    def _map_status(self, response_code: str) -> ServiceResponseStatus:
        """Map vendor-specific response codes to standard status."""
        return self.STATUS_MAPPINGS.get(response_code, ServiceResponseStatus.Failed)

    def _extract_response_details(self, response, params) -> EducationResponseDetails:
        """Extract and format response details. To be implemented by subclasses."""
        return EducationResponseDetails(
            provider=params.provider.value,
            service_type=params.service_type.value,
        )
