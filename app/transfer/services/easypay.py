from common.dtos import ServiceResponseData
from integration.nibbs.easypay.client import EasypayClient
from transfer.contracts import FundsTransferServiceInterface
from transfer.dtos import FundsTransferParams, NameEnquiryParams


class EasyPayTransferService(FundsTransferServiceInterface):

    def __init__(self):
        self.client = EasypayClient()

    def funds_transfer(self, params: FundsTransferParams) -> ServiceResponseData:
        pass

    def name_enquiry(self, network: NameEnquiryParams) -> list:
        pass
