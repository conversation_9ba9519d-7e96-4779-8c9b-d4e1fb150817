from datetime import datetime
from typing import Optional

from django.utils.translation import gettext_lazy as _
from rest_framework import exceptions
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.utils import datetime_from_epoch


def check_blacklisted_token(iat, user_id):
    claim_time = datetime_from_epoch(iat).strftime("%Y-%m-%d %H:%M:%S")
    blacklisted_token = BlacklistedToken.objects.filter(
        token__user__id=user_id, token__created_at__startswith=claim_time
    ).first()
    if blacklisted_token:
        raise exceptions.AuthenticationFailed(
            _("Token is blacklisted/invalidated"), code="token_not_valid"
        )


class AuthToken(AccessToken):
    def check_exp(
        self, claim: str = "exp", current_time: Optional[datetime] = None
    ) -> None:
        """
        Checks whether a timestamp value in the given claim has passed (since
        the given datetime value in `current_time`).  Raises a TokenError with
        a user-facing error message if so.
        """
        super().check_exp(claim, current_time)
        # user_id = self.payload[api_settings.USER_ID_CLAIM]
        # iat = self.payload["iat"]
        # check_blacklisted_token(iat, user_id)
