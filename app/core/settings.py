import logging
import os
import socket
from datetime import <PERSON><PERSON><PERSON>
from email.headerregistry import Address
from pathlib import Path

import hvac
import sentry_sdk
from kombu import Exchange, Queue
from sentry_sdk.integrations.django import DjangoIntegration

logging.basicConfig(level=logging.INFO)

vault_client = hvac.Client(
    url=os.environ.get("ATHENA_VAULT_URL"),
    token=os.environ.get("ATHENA_VAULT_TOKEN"),
)

vault_keys = vault_client.secrets.kv.read_secret_version(
    path=os.environ.get("ATHENA_VAULT_KEY")
)["data"]["data"]

BASE_DIR = Path(__file__).resolve().parent.parent
SECRET_KEY = vault_keys["SECRET_KEY"]
DEBUG = int(os.environ.get("DEBUG", 1))
APP_DESCRIPTION = os.environ.get("APP_DESCRIPTION", "App Name")

ALLOWED_HOSTS = [
    "127.0.0.1",
    "0.0.0.0",
    "localhost",
]

DEBUG_TOOLBAR_CONFIG = {
    "SHOW_TOOLBAR_CALLBACK": lambda request: DEBUG,
}
INTERNAL_IPS = ["127.0.0.1", "0.0.0.0", "**********", "localhost"]
if DEBUG:
    hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
    INTERNAL_IPS = [ip[:-1] + "1" for ip in ips] + ["127.0.0.1", "********"] + ips

    # INTERNAL_IPS += [".".join(ip.split(".")[:-1] + ["1"]) for ip in ips]

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Application definition

INSTALLED_APPS = [
    # 'jazzmin',
    "channels",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "storages",
    "rest_framework",
    "rest_framework_api_key",
    "django_filters",
    "import_export",
    "debug_toolbar",
    "drf_spectacular",
    "drf_standardized_errors",
    "django_extensions",
    "core.celery.CeleryConfig",
    "django_celery_beat",
    "user.apps.UserConfig",
    "data.apps.DataConfig",
    "electricity.apps.ElectricityConfig",
    "airtime.apps.AirtimeConfig",
    "epins.apps.EpinsConfig",
    "transfer.apps.TransferConfig",
    "cable_tv.apps.CableTvConfig",
    "betting.apps.BettingConfig",
    "kyc.apps.KycConfig",
    "virtual_account.apps.VirtualAccountConfig",
    "provider.apps.ProviderSettingsConfig",
    "education.apps.EducationConfig",
]

AUTH_USER_MODEL = "user.User"

MIDDLEWARE = [
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # 'core.middleware.CaptureExceptionMiddleware',
    # 'core.middleware.RequestResponseLoggerMiddleware',
    # 'pykolofinance.audtilog.logger.APILoggerMiddleware',
    'pykolofinance.audtilog.console.RequestResponseLoggerMiddleware',
]

ROOT_URLCONF = "core.urls"
IMPORT_EXPORT_USE_TRANSACTIONS = True

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"
ASGI_APPLICATION = "core.asgi.application"
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_HEADERS = [
    "authorization",
    "content-type",
    "x-api-key",  # Include your custom header here
    "accept",
    "origin",
    "user-agent",
]

CSRF_TRUSTED_ORIGINS = ["https://*.cintrustmfb.com", "https://api.creditassistng.com"]
LOGIN_URL = "rest_framework:login"
LOGOUT_URL = "rest_framework:logout"

JAZZMIN_SETTINGS = {
    "site_title": APP_DESCRIPTION,
    "site_header": APP_DESCRIPTION,
}

# Database

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("POSTGRES_DB"),
        "USER": os.environ.get("POSTGRES_USER"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
        "HOST": os.environ.get("POSTGRES_HOST"),  # or your database host
        "PORT": os.environ.get("POSTGRES_PORT"),
    },
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"
TIME_ZONE = "Africa/Lagos"
USE_I18N = True
USE_TZ = True

DATE_INPUT_FORMATS = [
    "%d/%m/%Y",
    "%d/%m/%y",  # '10/02/2020', '10/02/20'
    "%Y-%m-%d",
    "%m/%d/%Y",
    "%m/%d/%y",  # '2006-10-25', '10/25/2006', '10/25/06'
    "%b %d %Y",
    "%b %d, %Y",  # 'Oct 25 2006', 'Oct 25, 2006'
    "%d %b %Y",
    "%d %b, %Y",  # '25 Oct 2006', '25 Oct, 2006'
    "%B %d %Y",
    "%B %d, %Y",  # 'October 25, 2006', 'October 25, 2006'
    "%d %B %Y",
    "%d %B, %Y",  # '25 October 2006', '25 October 2006'
]

REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_PAGINATION_CLASS": "common.pagination.CustomPagination",
    "PAGE_SIZE": 12,
    # 'DATE_INPUT_FORMATS': ["%d/%m/%Y", ],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
    "EXCEPTION_HANDLER": "drf_standardized_errors.handler.exception_handler",
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",  # For unauthenticated users
        "rest_framework.throttling.UserRateThrottle",  # For authenticated users
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "100/minute",  # 50 requests per minute for anonymous users (per IP)
        "user": "60/minute",  # 50 requests per minute for authenticated users
        "password_reset": "5/minute",  # 2 requests per minute per IP
        "transaction": "3/minute",
        "login_pin": "5/minute",
        "transaction_pin": "5/minute",
        "create_password": "5/minute",
    },
}

DRF_STANDARDIZED_ERRORS = {
    "EXCEPTION_FORMATTER_CLASS": "core.middleware.DrfExceptionFormatter",
    "ENABLE_IN_DEBUG_FOR_UNHANDLED_EXCEPTIONS": True,
}

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

APP_NAME = os.getenv("APP_NAME")
STATIC_LOCATION = f"{APP_NAME}/static"
MEDIA_LOCATION = f"{APP_NAME}/media"
AWS_DEFAULT_ACL = "public-read"
AWS_QUERYSTRING_AUTH = False
AWS_S3_SIGNATURE_VERSION = "s3v4"
AWS_S3_ADDRESSING_STYLE = "virtual"
AWS_ACCESS_KEY_ID = vault_keys["ACCESS_KEY_ID"]
AWS_SECRET_ACCESS_KEY = vault_keys["ACCESS_SECRET"]
AWS_STORAGE_BUCKET_NAME = vault_keys["BUCKET_NAME"]
AWS_S3_REGION_NAME = vault_keys["REGION_NAME"]
AWS_S3_ENDPOINT_URL = f"https://{AWS_S3_REGION_NAME}.digitaloceanspaces.com"
AWS_S3_CUSTOM_DOMAIN = vault_keys["CUSTOM_DOMAIN"]
AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}
AWS_LOCATION = STATIC_LOCATION
STATIC_URL = f"https://{AWS_S3_ENDPOINT_URL}/{AWS_LOCATION}/"
# public media settings
PUBLIC_MEDIA_LOCATION = MEDIA_LOCATION
MEDIA_URL = f"https://{AWS_S3_ENDPOINT_URL}/{PUBLIC_MEDIA_LOCATION}/"
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
STATICFILES_STORAGE = "storages.backends.s3boto3.S3StaticStorage"

IMPORT_EXPORT_TMP_STORAGE_CLASS = "import_export.tmp_storages.MediaStorage"

# Using STORAGES for static and media files

STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "access_key": AWS_ACCESS_KEY_ID,
            "secret_key": AWS_SECRET_ACCESS_KEY,
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "region_name": AWS_S3_REGION_NAME,
            "endpoint_url": AWS_S3_ENDPOINT_URL,
            "custom_domain": AWS_S3_CUSTOM_DOMAIN,
            "location": AWS_LOCATION,
            "default_acl": AWS_DEFAULT_ACL,
            "object_parameters": {
                "CacheControl": "max-age=86400",
            },
        },
    },
    "staticfiles": {"BACKEND": "storages.backends.s3.S3Storage"},
    "import_export": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "region_name": AWS_S3_REGION_NAME,
            "access_key": AWS_ACCESS_KEY_ID,
            "secret_key": AWS_SECRET_ACCESS_KEY,
        },
    },
}
# Static and media URLs using custom domain

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "format": (
                '{"timestamp": "%(asctime)s", '
                '"level": "%(levelname)s", '
                '"message": "%(message)s", '
                '"module": "%(module)s", '
                '"function": "%(funcName)s", '
                '"line": %(lineno)d}'
            )
        },
        "colored": {
            "()": "colorlog.ColoredFormatter",
            "format": "%(asctime)s:%(log_color)s%(levelname)s:%(name)s:%(message)s ------------",
            "datefmt": "%Y-%m-%d %H:%M:%S",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "bold_red",
            },
        },
        "verbose": {
            "format": (
                "\n--- %(levelname)s ---\n"
                "Timestamp: %(asctime)s\n"
                "Message: %(message)s\n"
                "Location: %(pathname)s:%(lineno)d in %(funcName)s\n"
                "-------------------------------------------------\n"
            ),
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "colored",
        },
        "opensearch": {
            "level": "ERROR",
            "class": "core.loghandler.OpenSearchLogHandler",  # Use Celery for logging
            "formatter": "json",
        },
    },
    "loggers": {
        # Catch all Django-related logs here
        "django": {
            "handlers": ["console", "opensearch"],
            "level": "DEBUG",
            "propagate": False,
        },
    },
    # Root logger for all other logs
    "root": {
        "handlers": ["console", "opensearch"],
        "level": "INFO",
    },
}

# Email Settings
EMAIL_FROM = Address(
    display_name="CreditAssist", addr_spec=os.environ.get("SENDER_EMAIL")
)
EMAIL_HOST = os.environ.get("SMTP_HOST")
EMAIL_HOST_USER = os.environ.get("SMTP_USER")
EMAIL_HOST_PASSWORD = os.environ.get("SMTP_PASSWORD")
EMAIL_PORT = os.environ.get("SMTP_PORT", 587)
EMAIL_USE_TLS = True

DEFAULT_TIMEOUT = 30

CLIENT_URL = os.environ.get("CLIENT_URL")
TOKEN_LIFESPAN = 5 * 60  # seconds
ONBOARDING_TOKEN_LIFESPAN = 5 * 60  # in seconds

TEAM_ONBOARD_TOKEN = 604800  # in seconds for 7 days
RESET_TOKEN_LIFESPAN = 10 * 60  # in seconds

REDIS_URL = os.environ.get("REDIS_URL")

CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = REDIS_URL
CELERY_SEND_TASK_SENT_EVENT = True
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
CELERY_TASK_RESULT_EXPIRES = 18000

FLOWER_BASIC_AUTH = os.environ.get("FLOWER_BASIC_AUTH")
# Assumes that the username is swift or simple have the url as redis://swift:jetSwift@localhost:6379/0
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
        "KEY_PREFIX": APP_NAME,
    }
}

SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"

CACHE_TTL = 60 * 1
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [REDIS_URL],
        },
        # 'ROUTING': 'core'
    },
}

CELERY_QUEUES = (Queue("logging_queue", Exchange("logging"), routing_key="logging"),)

# Define the routing rules for specific tasks
CELERY_ROUTES = {
    "core.tasks.send_log_to_opensearch": {"queue": "logging_queue"},
}

CELERY_BEAT_SCHEDULE = {
    # "sample_task": {
    #     "task": "user.tasks.sample_task",
    #     "schedule": crontab(minute="*/1"),
    # },
    # "send_email_report": {
    #     "task": "user.tasks.send_email_report",
    #     "schedule": crontab(hour="*/1"),
    # },
}

SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"}
    },
}

SPECTACULAR_SETTINGS = {
    "SCHEMA_PATH_PREFIX": r"/api/v1",
    "DEFAULT_GENERATOR_CLASS": "drf_spectacular.generators.SchemaGenerator",
    "SERVE_PERMISSIONS": ["rest_framework.permissions.AllowAny"],
    "COMPONENT_SPLIT_PATCH": True,
    "COMPONENT_SPLIT_REQUEST": True,
    "SWAGGER_UI_SETTINGS": {
        "deepLinking": True,
        "persistAuthorization": True,
        "displayOperationId": True,
        "displayRequestDuration": True,
    },
    "UPLOADED_FILES_USE_URL": True,
    "TITLE": APP_DESCRIPTION,
    "DESCRIPTION": f"{APP_DESCRIPTION} Doc",
    "VERSION": "1.0.0",
    "LICENCE": {"name": "BSD License"},
    "CONTACT": {"name": "Daniel Ale", "email": "<EMAIL>"},
    # Oauth2 related settings. used for example by django-oauth2-toolkit.
    # https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.3.md#oauth-flows-object
    "OAUTH2_FLOWS": [],
    "OAUTH2_AUTHORIZATION_URL": None,
    "OAUTH2_TOKEN_URL": None,
    "OAUTH2_REFRESH_URL": None,
    "OAUTH2_SCOPES": None,
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=7),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=14),
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_TOKEN_CLASSES": ("core.authentication.AuthToken",),
}

if DEBUG == 0:
    sentry_sdk.init(
        dsn=os.environ.get("SENTRY_DSN", None),
        integrations=[DjangoIntegration()],
        traces_sample_rate=1.0,
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
    )


MONGODB_LOGGER_URL = vault_keys["MONGODB_LOGGER_URL"]
MONGODB_LOGGER_DATABASE = "savings"
API_LOGGER_EXCLUDE_KEYS = [
    "tx_pin",
    "bvn",
    "nin",
    "X-KMS-KEY",
    "AUTHORIZATION",
    "X-API-KEY",
    "x-api-key",
    "access",
    "refresh",
    "X_CSRFTOKEN",
    "X_API_KEY",
]
DRF_API_LOGGER_EXCLUDE_KEYS = [
    "tx_pin",
    "bvn",
    "nin",
    "X-KMS-KEY",
    "AUTHORIZATION",
    "X-API-KEY",
    "x-api-key",
    "access",
    "refresh",
    "X_CSRFTOKEN",
    "X_API_KEY",
]

ELK_LOGGER_URL = os.environ.get("ELK_LOGGER_URL")
API_LOGGER_SKIP_URL_NAME = ["schema"]
ENVIRONMENT_INSTANCE = os.environ.get("ENVIRONMENT_INSTANCE", "dev")


API_KEY_CUSTOM_HEADER = "HTTP_X_API_KEY"

VANSO_SYSID = vault_keys["VANSO_SYSID"]
VANSO_PASSWORD = vault_keys["VANSO_PASSWORD"]
VANSO_SENDER = vault_keys["VANSO_SENDER"]

SHAGO_BASE_URL = os.environ.get("SHAGO_BASE_URL")
SHAGO_HASH_KEY = os.environ.get("SHAGO_HASH_KEY")
GRACEHUB_BASE_URL = os.environ.get("GRACEHUB_BASE_URL")
GRACEHUB_AUTH = os.environ.get("GRACEHUB_AUTH")

SERVICE_CODES = {
    "validate_meter": "AOV",
    "purchase_airtime": "QAB",
    "purchase_electricity": "AOB",
    "get_data_bundle": "VDA",
    "data_vending": "BDA",
    "tv_validation": "GDS",
    "dstv_purchase": "GDB",
    "get_current_multichoice_sub": "MULTICHOICE",
    "dstv_box_office_purchase": "GDB",
    "multichoice_payment_renewal": "GDB",
    "dstv_addons": "ADN",
    "dstv_addon_purchase": "GDB",
    "gotv_purchase": "GDB",
    "startime_purchase": "GDB",
    "tv_bouqute": "TV_PACKAGES",
    "multichoice_bouqute": "MUL",
    "bet_account_validation": "BEV",
    "bet_account_payment": "BEP",
    "education_verify": "EDV",
    "education_purchase": "EDP",
}

SONITE_API_SECRET = os.environ.get("SONITE_API_SECRET")
SONITE_API_TOKEN = os.environ.get("SONITE_API_TOKEN")
SONITE_MERCHANT_CODE = os.environ.get("SONITE_MERCHANT_CODE")
SONITE_BASE_URL = os.environ.get("SONITE_BASE_URL")

EASYPAY_BASE_URL = os.environ.get("EASYPAY_BASE_URL")
EASYPAY_APP_NAME = os.environ.get("EASYPAY_APP_NAME")
EASYPAY_CLIENT_ID = os.environ.get("EASYPAY_CLIENT_ID")
EASYPAY_CLIENT_SECRET = os.environ.get("EASYPAY_CLIENT_SECRET")
EASYPAY_CLIENT_CODE = os.environ.get("EASYPAY_CLIENT_CODE")
EASYPAY_BILLER_ID = os.environ.get("EASYPAY_BILLER_ID")
EASYPAY_CHANNEL_CODE = os.environ.get("EASYPAY_CHANNEL_CODE", 3)
EASYPAY_LOCATION = os.environ.get("EASYPAY_LOCATION", "7.42650,3.91042")
EASYPAY_BANKS = {
    "ecobank": {
        "institution_code": "000010",
        "account_number": os.environ.get("EASYPAY_ECOBANK_ACCOUNT_NUMBER"),
        "account_name": os.environ.get("EASYPAY_ECOBANK_ACCOUNT_NAME", "ERC"),
        "bvn": os.environ.get("EASYPAY_ECOBANK_BVN", "***********"),
        "kyc_level": os.environ.get("EASYPAY_ECOBANK_KYC_LEVEL", 3),
        "authorization_code": os.environ.get("EASYPAY_ECOBANK_MANDATE"),
    },
    "fidelity": {
        "institution_code": "000007",
        "account_number": os.environ.get("EASYPAY_FIDELITY_ACCOUNT_NUMBER"),
        "account_name": os.environ.get(
            "EASYPAY_FIDELITY_ACCOUNT_NAME", "ERCAS INTEGRATED SOLUTIONS LIMITED"
        ),
        "bvn": os.environ.get("EASYPAY_FIDELITY_BVN", "***********"),
        "kyc_level": os.environ.get("EASYPAY_FIDELITY_KYC_LEVEL", 3),
        "authorization_code": os.environ.get("EASYPAY_FIDELITY_MANDATE"),
    },
    "zenith": {
        "institution_code": "000015",
        "account_number": os.environ.get("EASYPAY_ZENITH_ACCOUNT_NUMBER"),
        "account_name": os.environ.get("EASYPAY_ZENITH_ACCOUNT_NAME", "Ercas"),
        "bvn": os.environ.get("EASYPAY_ZENITH_BVN", "***********"),
        "kyc_level": os.environ.get("EASYPAY_ZENITH_KYC_LEVEL", 3),
        "authorization_code": os.environ.get("EASYPAY_ZENITH_MANDATE"),
    },
    "wema": {
        "institution_code": "000017",
        "account_number": os.environ.get("EASYPAY_WEMA_ACCOUNT_NUMBER"),
        "account_name": os.environ.get("EASYPAY_WEMA_ACCOUNT_NAME", "Ercas"),
        "bvn": os.environ.get("EASYPAY_WEMA_BVN", "***********"),
        "kyc_level": os.environ.get("EASYPAY_WEMA_KYC_LEVEL", 3),
        "authorization_code": os.environ.get("EASYPAY_WEMA_MANDATE"),
    },
}

JAMB_BASE_URL = os.environ.get("JAMB_BASE_URL")
JAMB_SELLING_POINT = os.environ.get("JAMB_SELLING_POINT")
JAMB_SECURITY_TOKEN = os.environ.get("JAMB_SECURITY_TOKEN")

YOU_VERIFY_API_KEY = os.environ.get("YOU_VERIFY_API_KEY")
YOU_VERIFY_BASE_URL = os.environ.get("YOU_VERIFY_BASE_URL")

ACCESS_BASE_URL = os.environ.get("ACCESS_BASE_URL")
ACCESS_SUBSCRIPTION_KEY = os.environ.get("ACCESS_SUBSCRIPTION_KEY")
ACCESS_CHANNEL_CODE = os.environ.get("ACCESS_CHANNEL_CODE")
ACCESS_MERCHANT_ID = os.environ.get("ACCESS_MERCHANT_ID")
ACCESS_AUTHORIZATION_KEY = os.environ.get("ACCESS_AUTHORIZATION_KEY")

CAPRICORN_BASE_URL = os.environ.get("CAPRICORN_BASE_URL")
CAPRICORN_X_API_KEY = os.environ.get("CAPRICORN_X_API_KEY")
CAPRICORN_AGENT_ID = os.environ.get("CAPRICORN_AGENT_ID")
BLUSALT_BASE_URL = os.environ.get("BLUSALT_BASE_URL")
BLUSALT_API_KEY = os.environ.get("BLUSALT_API_KEY")
BLUSALT_CLIENT_ID = os.environ.get("BLUSALT_CLIENT_ID")
BLUSALT_APP_NAME = os.environ.get("BLUSALT_APP_NAME")
BLUSALT_PUBLIC_KEY = os.environ.get("BLUSALT_PUBLIC_KEY")
BLUSALT_SECRET_KEY = os.environ.get("BLUSALT_SECRET_KEY")

MAMA_AFRICA_API_KEY = os.environ.get("MAMA_AFRICA_API_KEY")
MAMA_AFRICA_BASE_URL = os.environ.get("MAMA_AFRICA_BASE_URL")

QUICKTELLER_BASE_URL = os.environ.get('QUICKTELLER_BASE_URL')
QUICKTELLER_CLIENT_ID = os.environ.get('QUICKTELLER_CLIENT_ID')
QUICKTELLER_SECRET_KEY = os.environ.get('QUICKTELLER_SECRET_KEY')
QUICKTELLER_TERMINAL_ID = os.environ.get('QUICKTELLER_TERMINAL_ID')
QUICKTELLER_TOKEN_URL = os.environ.get('QUICKTELLER_TOKEN_URL')