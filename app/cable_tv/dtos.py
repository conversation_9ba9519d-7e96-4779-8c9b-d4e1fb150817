from dataclasses import dataclass
from typing import Optional

from cable_tv.enums import CableProviderEnum
from common.utils import ToDict
from integration.utils import FieldMapper


@dataclass(init=False)
class ValidateIucNumberParams(ToDict, FieldMapper):
    iuc_number: str
    provider: CableProviderEnum

@dataclass
class SubscribeCableTVParams(ToDict):
    iuc_number: str
    reference: str
    central_plan: str
    provider: CableProviderEnum
    customer_name: Optional[str] = None
    has_add_on: Optional[bool] = False
    period: Optional[int] = 1

@dataclass
class CorrespondingCableBillerDetails(ToDict):
    code: str
    price: float
    name: str

@dataclass
class ValidatedCableDetails(ToDict):
    iuc_number: str
    customer_name: str