import logging
from typing import Dict, Any, Optional, Tuple

from django.core.cache import cache

from cable_tv.contracts import CableTVServiceInterface
from cable_tv.dtos import (
    SubscribeCableTVParams,
    ValidateIucNumberParams,
    CorrespondingCableBillerDetails,
    ValidatedCableDetails
)
from cable_tv.exceptions import InvalidCablePlanException
from cable_tv.models import ShagoCableTvPlan
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus
from integration.shago.client import ShagoClient
from integration.shago.dto import CableTVPurchasePayload, ValidateIucNumberPayload

logger = logging.getLogger(__name__)


class ShagoCableTvService(CableTVServiceInterface):
    """Shago implementation of Cable TV service interface."""

    def __init__(self):
        self.client = ShagoClient()

    def subscribe(self, params: SubscribeCableTVParams) -> ServiceResponseData:
        try:
            # Validate IUC number first
            validation_result = self._validate_iuc_for_subscription(params)
            if validation_result.status == ServiceResponseStatus.Failed.value:
                return validation_result

            # Process subscription
            return self._process_subscription(params, validation_result.data)

        except Exception as e:
            logger.exception(f"Cable TV subscription failed for IUC {params.iuc_number}: {e}")
            return self._create_error_response(ServiceResponseStatus.Pending.value)

    def validate(self, params: ValidateIucNumberParams) -> ServiceResponseData:
        # Check cache first
        cached_result = self._get_cached_validation(params)
        if cached_result:
            return cached_result

        try:
            return self._perform_iuc_validation(params)
        except Exception as e:
            logger.exception(f"IUC validation failed for number {params.iuc_number}: {e}")
            return self._create_error_response()

    def _validate_iuc_for_subscription(self, params: SubscribeCableTVParams) -> ServiceResponseData:
        validate_params = ValidateIucNumberParams(iuc_number=params.iuc_number,provider=params.provider)
        validation_result = self._perform_iuc_validation(validate_params)

        if validation_result.status == ServiceResponseStatus.Failed.value:
            return ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="IUC number validation failed."
            )

        return validation_result

    def _process_subscription(self, params: SubscribeCableTVParams, validated_data: Dict[str, Any]) -> ServiceResponseData:
        """Process the actual subscription after validation."""
        validated_details = ValidatedCableDetails(**validated_data)
        params.customer_name = validated_details.customer_name

        biller_details = self._get_biller_details(params.central_plan)
        payload = self._build_subscription_payload(params, biller_details)

        status_code, response = self.client.purchase_tv(payload)
        return self._handle_subscription_response(response, validated_details)

    @staticmethod
    def _handle_subscription_response(response, validated_details: ValidatedCableDetails) -> ServiceResponseData:
        if response.status == "200":
            return ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message="Cable tv subscription successful.",
                data=validated_details.to_dict()
            )

        if response.status == "400":
            return ServiceResponseData(
                status=ServiceResponseStatus.Pending.value,
                message="Cable tv subscription pending.",
                data=validated_details.to_dict()
            )

        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message="Cable tv subscription failed.",
            data=validated_details.to_dict()
        )


    def _get_cached_validation(self, params: ValidateIucNumberParams) -> Optional[ServiceResponseData]:
        cache_key = self._build_cache_key(params)
        cached_details = cache.get(cache_key)

        if cached_details:
            return ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message="IUC number validated successfully",
                data=cached_details
            )

        return None

    def _perform_iuc_validation(self, params: ValidateIucNumberParams) -> ServiceResponseData:
        payload = self._build_validation_payload(params)
        status_code, response = self.client.tv_validation(payload)

        if response.status == "200":
            validated_data = response.to_dict()

            # Cache successful validation
            cache_key = self._build_cache_key(params)
            cache.set(cache_key, validated_data, 3600) #Cache for 1 hour

            return ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message="IUC number validated successfully",
                data=validated_data
            )

        return ServiceResponseData(
            status=ServiceResponseStatus.Failed.value,
            message=response.message,
            data=None,
            status_code=status_code
        )

    @staticmethod
    def _build_cache_key(params: ValidateIucNumberParams) -> str:
        """Build cache key for IUC validation."""
        return f"cable-tv:validate:{params.iuc_number}:{params.provider.value}"

    @staticmethod
    def _get_biller_details(central_plan: str) -> CorrespondingCableBillerDetails:
        """
        Get Shago biller details using the Central Plan.
        """
        plan = ShagoCableTvPlan.objects.filter(central_plan=central_plan).first()
        if not plan:
            raise InvalidCablePlanException("Invalid cable plan selected for this provider")

        return CorrespondingCableBillerDetails(price=float(plan.price),code=plan.code,name=plan.name)

    @staticmethod
    def _build_subscription_payload(
            params: SubscribeCableTVParams,
            biller_details: CorrespondingCableBillerDetails
    ) -> CableTVPurchasePayload:
        return CableTVPurchasePayload(
            smartCardNo=params.iuc_number,
            customerName=params.customer_name,
            type=params.provider.value,
            amount=str(biller_details.price),
            packagename=biller_details.name,
            productsCode=biller_details.code,
            period=str(params.period),
            hasAddon="1" if params.has_add_on else "0",
            request_id=params.reference
        )

    @staticmethod
    def _build_validation_payload(params: ValidateIucNumberParams) -> ValidateIucNumberPayload:
        return ValidateIucNumberPayload(
            smartCardNo=params.iuc_number,
            type=params.provider.value
        )

    @staticmethod
    def _create_error_response(
            status: Optional[str] = None,
            message: Optional[str] = None
    ) -> ServiceResponseData:
        return ServiceResponseData(
            status=status or ServiceResponseStatus.Failed.value,
            message=message or GENERIC_EXCEPTION_ERROR_MESSAGE,
            data=None
        )