# Generated by Django 5.1.5 on 2025-05-25 11:23

import common.kgs
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CentralCableTvPlan',
            fields=[
                ('id', models.CharField(default=common.kgs.generate_unique_id, editable=False, max_length=50, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.CharField(choices=[('GOTV', 'GOTV'), ('DSTV', 'DSTV'), ('STARTIMES', 'STARTIMES')], db_index=True, max_length=50)),
                ('code', models.CharField(db_index=True, max_length=100)),
                ('name', models.CharField(max_length=150)),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('duration', models.CharField(max_length=30)),
            ],
            options={
                'ordering': ('provider',),
            },
        ),
        migrations.CreateModel(
            name='ShagoCableTvPlan',
            fields=[
                ('id', models.CharField(default=common.kgs.generate_unique_id, editable=False, max_length=50, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.CharField(choices=[('GOTV', 'GOTV'), ('DSTV', 'DSTV'), ('STARTIMES', 'STARTIMES')], db_index=True, max_length=50)),
                ('code', models.CharField(db_index=True, max_length=100)),
                ('name', models.CharField(max_length=150)),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('duration', models.CharField(max_length=30)),
                ('central_plan', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='shago_cable_tv_plan', to='cable_tv.centralcabletvplan')),
            ],
            options={
                'ordering': ('provider',),
            },
        ),
    ]
