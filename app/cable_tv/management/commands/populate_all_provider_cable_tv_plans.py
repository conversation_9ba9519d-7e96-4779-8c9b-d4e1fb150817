from decimal import Decimal
from django.utils.text import slugify
from django.core.management.base import BaseCommand

from cable_tv.enums import CableProviderEnum
from cable_tv.models import CentralCableTvPlan, ShagoCableTvPlan
from integration.shago.client import ShagoClient


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.stdout.write("Populating TV plans for each provider...")

        shago_client = ShagoClient()

        for provider_enum in CableProviderEnum:
            provider = provider_enum.value
            central_plans = self.__fetch_central_plans(provider)

            if not central_plans:
                self.stdout.write(f"No central plans found for {provider}. Please populate them first.")
                continue

            _, response = shago_client.tv_bouquet(provider)
            if response.status != "200":
                self.stdout.write(f"Skipping {provider}: status = {response.status}")
                continue

            self.__populate_shago_plans(provider, central_plans, response.product.get(provider.upper(), []))

        self.stdout.write("TV plans populated successfully for all providers.")

    @staticmethod
    def __fetch_central_plans(provider: str):
        return list(CentralCableTvPlan.objects.filter(provider=provider))

    def __populate_shago_plans(self, provider: str, central_plans, products: list):
        if not products:
            self.stdout.write(f"No products found for {provider}")
            return

        # Delete all old Shago plans for this provider
        ShagoCableTvPlan.objects.filter(provider=provider).delete()

        central_price_map = {float(plan.price): plan for plan in central_plans}
        seen_prices = set()
        result = []

        for product in products:
            try:
                shago_price = float(product.get("price", 0))
            except (ValueError, TypeError):
                continue

            if shago_price in seen_prices:
                self.stdout.write(self.style.WARNING(f"Duplicate price {shago_price} for {provider}, skipping."))
                continue

            # Find the closest matching central price (within range)
            central_plan = self.__find_matching_central_plan(central_price_map, shago_price)
            if not central_plan:
                self.stdout.write(
                    self.style.WARNING(f"No matching central plan for {provider}: Shago price = {shago_price}")
                )
                continue

            seen_prices.add(shago_price)

            self.stdout.write(
                self.style.SUCCESS(f"Matched price for {provider}: {shago_price} with central {central_plan.price}")
            )

            result.append(
                ShagoCableTvPlan(
                    central_plan=central_plan,
                    provider=provider,
                    name=product.get('name'),
                    price=Decimal(str(shago_price)),
                    code=product.get('code'),
                    duration=slugify(product.get('duration') or f"{product.get('month')} month"),
                )
            )

        ShagoCableTvPlan.objects.bulk_create(result)

    @staticmethod
    def __find_matching_central_plan(price_map, shago_price: float):
        """Return the central plan whose price is within ±2 of shago_price."""
        for central_price, plan in price_map.items():
            if abs(central_price - shago_price) <= 2:
                return plan
        return None
