from decimal import Decimal
from django.utils.text import slugify
from django.core.management.base import BaseCommand
from cable_tv.enums import CableProviderEnum
from cable_tv.models import CentralCableTvPlan
from integration.shago.client import ShagoClient
from integration.shago.dto import TVBouquetItem


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.stdout.write("Populating central plans...")
        client = ShagoClient()

        for provider in CableProviderEnum:
            _, response = client.tv_bouquet(provider.value)
            if response.status != "200":
                self.stdout.write(f"Skipping {provider.value}: status = {response.status}")
                continue

            CentralCableTvPlan.objects.filter(provider=provider.value).delete()

            result = []
            products = response.product[provider.value.upper()]

            for product in  products:
                result.append(
                    CentralCableTvPlan(
                        provider=provider.value,
                        name=product.get('name', None),
                        price=Decimal(product.get('price', None)),
                        code=product.get('code', None),
                        duration=slugify(product.get('duration') or f"{product.get('month')}"),
                    )
                )

            CentralCableTvPlan.objects.bulk_create(result)
            result.clear()

        self.stdout.write(f"Populated central plans for all providers successfully!")