from cable_tv.enums import CableProviderEnum
from common.models import AuditableModel
from django.db import models

class CentralCableTvPlan(AuditableModel):
    provider = models.CharField(max_length=50, choices=CableProviderEnum.choices(), db_index=True)
    code = models.CharField(max_length=100, db_index=True)
    name = models.CharField(max_length=150)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    duration = models.CharField(max_length=30)

    objects = models.Manager()

    def __str__(self):
        return f"{self.name} ({self.provider})"

    class Meta:
        ordering = ("provider",)

class ShagoCableTvPlan(AuditableModel):
    provider = models.CharField(max_length=50, choices=CableProviderEnum.choices(), db_index=True)
    central_plan = models.OneToOneField(
        "cable_tv.CentralCableTvPlan", on_delete=models.PROTECT, related_name="shago_cable_tv_plan"
    )
    code = models.CharField(max_length=100, db_index=True)
    name = models.CharField(max_length=150)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    duration = models.CharField(max_length=30)

    objects = models.Manager()

    def __str__(self):
        return f"{self.name} ({self.provider})"

    class Meta:
        ordering = ("provider",)