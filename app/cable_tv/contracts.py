from abc import ABC, abstractmethod

from cable_tv.dtos import SubscribeCableTVParams, ValidateIucNumberParams
from common.dtos import ServiceResponseData


class CableTVServiceInterface(ABC):
    @abstractmethod
    def subscribe(self, params: SubscribeCableTVParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def validate(self, params: ValidateIucNumberParams) -> ServiceResponseData:
        pass
