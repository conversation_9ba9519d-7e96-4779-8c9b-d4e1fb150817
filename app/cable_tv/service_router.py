from typing import Optional

from cable_tv.contracts import CableTVServiceInterface
from cable_tv.services.shago import ShagoCableTvService
from common.enums import VenderEnum


class CableTvServiceRouter:
    """
    Routes cable TV service requests to the appropriate vendor-specific implementation.

    This class acts as a dynamic service router that delegates cable TV-related method calls
    (e.g., purchase, subscription management) to the correct service implementation based on
    the provided `driver` (vendor). This abstraction allows the business logic to interact
    with a unified interface while enabling easy switching between different cable TV service providers.
    """

    @staticmethod
    def resolve(driver: VenderEnum) -> CableTVServiceInterface:
        """
        Returns the appropriate cable TV service based on the driver.

        Returns:
            CableTVServiceInterface: The cable TV service instance.
        """
        return ShagoCableTvService()