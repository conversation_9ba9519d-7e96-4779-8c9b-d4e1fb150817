from django_filters.rest_framework.backends import DjangoFilterBackend
from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header
from pykolofinance.common.serializers import EmptySerializer
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_api_key.permissions import HasAPIKey
from cable_tv.models import CentralCableTvPlan
from cable_tv.v1.serializers import CentralCableTVPlansSerializer, ValidateIucNumberSerializer, \
    SubscribeCableTvSerializer
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus


@extend_schema_view(
    fetch_central_plans=header,
    validate=header,
    subscribe=header,
)
class CableTvViewSet(viewsets.GenericViewSet):
    http_method_names = ['post', 'get']
    permission_classes = [HasAPIKey]

    @action(
        methods=['post'],
        serializer_class=ValidateIucNumberSerializer,
        detail=False,
        url_path='validate'
    )
    def validate(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)

    @action(
        methods=['post'],
        serializer_class=SubscribeCableTvSerializer,
        detail=False,
        url_path='subscribe'
    )
    def subscribe(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)

    @action(
        methods=['get'],
        serializer_class=EmptySerializer,
        detail=False,
        url_path=r'fetch-central-plans/(?P<provider>[\w-]+)'
    )
    def fetch_central_plans(self, request, provider):
        if not provider:
            data = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="Provider is required."
            )
            return Response(data.to_dict(), status=data.status_code)

        central_plans = CentralCableTvPlan.objects.filter(provider=provider.upper())

        if not central_plans.exists():
            data = ServiceResponseData(
                status=ServiceResponseStatus.Failed.value,
                message="No plans found for this provider"
            )
        else:
            data = ServiceResponseData(
                status=ServiceResponseStatus.Success.value,
                message="Central plans fetched successfully",
                data=list(CentralCableTVPlansSerializer(central_plans, many=True).data)
            )

        return Response(data.to_dict(), status=data.status_code)
