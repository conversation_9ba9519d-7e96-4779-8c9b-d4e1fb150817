from rest_framework import serializers

from cable_tv.dtos import ValidateIucNumberParams, SubscribeCableTVParams
from cable_tv.enums import CableProviderEnum
from cable_tv.service_router import CableTvServiceRouter
from cable_tv.models import CentralCableTvPlan
from common.enums import ProductEnum, ProviderEnum
from provider.utils import get_vender


class CentralCableTVPlansSerializer(serializers.ModelSerializer):
    class Meta:
        model=CentralCableTvPlan
        fields='__all__'

class ValidateIucNumberSerializer(serializers.Serializer):
    provider = serializers.ChoiceField(choices=CableProviderEnum.choices())
    iuc_number = serializers.CharField()

    def validate(self, attrs):
        data = super().validate(attrs)
        data['provider'] = CableProviderEnum(data['provider'])
        return data

    def save(self):
        params = ValidateIucNumberParams(**self.validated_data)
        vender = get_vender(ProductEnum.CableTv, ProviderEnum(params.provider.value.title()))
        response = CableTvServiceRouter().resolve(vender).validate(params)

        return response


class SubscribeCableTvSerializer(serializers.Serializer):
    provider = serializers.ChoiceField(choices=CableProviderEnum.choices())
    iuc_number = serializers.CharField()
    code = serializers.CharField()
    reference =serializers.CharField()

    def validate(self, attrs):
        data = super().validate(attrs)
        code = attrs.get('code')
        central_plan = CentralCableTvPlan.objects.filter(code=code).first()
        if not central_plan:
            raise serializers.ValidationError({"code": "Invalid code selected"})

        data['provider'] = CableProviderEnum(data['provider'])
        data['central_plan'] = central_plan
        data.pop('code')
        return data

    def save(self):
        params = SubscribeCableTVParams(**self.validated_data)
        vender = get_vender(ProductEnum.CableTv, ProviderEnum(params.provider.value.title()))
        response = CableTvServiceRouter().resolve(vender).subscribe(params)

        return response


