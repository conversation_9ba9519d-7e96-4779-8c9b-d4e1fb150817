from services.shago_tv_service import TVService

service = TVService()
response, status_code = service.purchase_dstv_addon(
    smartCardNo="2022188682",
    customerName="Sten Sten ",
    type="DSTV ",
    amount="7900",
    packagename="DStv Access",
    productsCode="COMPE36",
    period="1",
    hasAddon="1",
    addonproductCode="ASIADDE36 ",
    addonAmount="6200",
    addonproductName="Asian Addon",
)
print(
    " successfully purchased dstv addon"
    if response is not None and int(status_code) == 200
    else "Failed to make dstv addon purchase"
)
