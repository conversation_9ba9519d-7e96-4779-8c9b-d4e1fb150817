from services.shago_tv_service import TVService

service = TVService()
response, status_code = service.purchase_gotv(
    amount="1640.00",
    type="GOTV",
    smartCardNo="7528379740",
    customerName="X",
    packagename="GOtv Jinja",
    productsCode="GOTVNJ1",
    period="1",
)
print(
    " gotv purchase successful"
    if response is not None and int(status_code) == 200
    else "gotv purchase failed"
)
