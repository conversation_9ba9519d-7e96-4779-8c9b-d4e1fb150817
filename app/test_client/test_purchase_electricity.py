# export PYTHONPATH in shell to run tests

from services.shago_electricity_service import ElectricityService

service = ElectricityService()
response, status_code = service.purchase_electricity(
    disco="IKEDC",
    meter_no="54150429196",
    meter_type="PREPAID",
    amount=100,
    phone_number="08168112963",
    name="TESTMETER1",
    address="ABULE-EGBA",
)
print(
    "Purchase Successful"
    if response is not None and int(status_code) == 200
    else "Purchase failed"
)
