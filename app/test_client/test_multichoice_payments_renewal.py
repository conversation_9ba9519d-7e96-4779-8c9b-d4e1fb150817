from services.shago_tv_service import TVService

service = TVService()
response, status_code = service.multichoice_payment_renewal_or_tender_amount(
    smartCardNo="2022188682",
    customerName="Sten Sten ",
    type="DSTV ",
    amount="2500",
    packagename="Renewal",
    period="1",
)
print(
    " successfully returned payment renewal amount"
    if response is not None and int(status_code) == 200
    else "Cannnot return payment renewal"
)
