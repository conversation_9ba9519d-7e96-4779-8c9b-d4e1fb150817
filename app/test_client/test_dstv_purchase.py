from services.shago_tv_service import TVService

service = TVService()
response, status_code = service.dstv_purchase(
    smartCardNo="2022188682",
    customerName="Sten Sten",
    type="DSTV",
    amount="25550",
    packagename="Dstv Access",
    productsCode="PRWFRNSE36",
    period="1",
    hasAddon="0",
)
print(
    "DSTV Purchase Successful"
    if response is not None and int(status_code) == 200
    else "DSTV Purchase failed"
)
