from abc import ABC, abstractmethod

from common.dtos import ServiceResponseData
from virtual_account.dtos import CreateVirtualAccountParams


class VirtualAccountServiceInterface(ABC):
    @abstractmethod
    def create(self, params: CreateVirtualAccountParams) -> ServiceResponseData:
        pass

    @abstractmethod
    def update(self, identifier: str) -> ServiceResponseData:
        pass

    @abstractmethod
    def delete(self, identifier: str) -> ServiceResponseData:
        pass
