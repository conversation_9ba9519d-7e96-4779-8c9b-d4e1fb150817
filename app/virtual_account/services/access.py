from common.dtos import ServiceResponseData
from integration.access_bank.virtual_account_client import (
    AccessBankVirtualAccountClient,
)
from virtual_account.contracts import VirtualAccountServiceInterface
from virtual_account.dtos import CreateVirtualAccountParams


class AccessVirtualAccountService(VirtualAccountServiceInterface):

    def __init__(self):
        self.client = AccessBankVirtualAccountClient()

    def create(self, params: CreateVirtualAccountParams) -> ServiceResponseData:
        pass

    def update(self, identifier: str) -> ServiceResponseData:
        pass

    def delete(self, identifier: str) -> ServiceResponseData:
        pass
