from common.dtos import ServiceResponseData
from virtual_account.contracts import VirtualAccountServiceInterface
from virtual_account.dtos import CreateVirtualAccountParams


class WemaVirtualAccountService(VirtualAccountServiceInterface):

    def create(self, params: CreateVirtualAccountParams) -> ServiceResponseData:
        pass

    def update(self, identifier: str) -> ServiceResponseData:
        pass

    def delete(self, identifier: str) -> ServiceResponseData:
        pass
