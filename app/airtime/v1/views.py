from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework_api_key.permissions import HasAPIKey
from rest_framework.response import Response
from airtime.v1.serializers import AirtimePurchaseSerializer
from common.dtos import ServiceResponseData
from drf_spectacular.utils import extend_schema_view
from pykolofinance.common.schema import header

@extend_schema_view(
    purchase_airtime=header,
)
class AirtimeViewSet(viewsets.GenericViewSet):
    http_method_names = ['post']
    permission_classes = [HasAPIKey]

    @action(
        methods=['POST'],
        serializer_class=AirtimePurchaseSerializer,
        detail=False,
        url_path="purchase-airtime"
    )
    def purchase_airtime(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data: ServiceResponseData = serializer.save()
        return Response(data.to_dict(), status=data.status_code)

