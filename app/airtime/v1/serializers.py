from rest_framework import serializers

from airtime.dtos import AirtimePurchaseParams
from airtime.enums import AirtimeNetworkEnum
from airtime.service_router import AirtimePurchaseServiceRouter
from common.enums import VenderEnum, ProductEnum, ProviderEnum
from provider.utils import get_vender


class AirtimePurchaseSerializer(serializers.Serializer):
    phone = serializers.CharField()
    network = serializers.ChoiceField(choices=AirtimeNetworkEnum.choices())
    reference = serializers.CharField()
    amount = serializers.IntegerField()

    def validate(self, attrs):
        data = super().validate(attrs)
        data['network'] = AirtimeNetworkEnum(data['network'])
        if data['amount'] <= 0:
            raise serializers.ValidationError({'amount': 'Amount must be greater than zero.'})
        return data

    def save(self):
        params = AirtimePurchaseParams(**self.validated_data)
        vender = get_vender(ProductEnum.Airtime, ProviderEnum(params.network.value.title()))
        response = AirtimePurchaseServiceRouter().resolve(vender).vend(params)

        return response