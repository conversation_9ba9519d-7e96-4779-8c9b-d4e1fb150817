from airtime.dtos import AirtimePurchaseParams, AirtimeResponseDetails
from airtime.enums import AirtimeNetworkEnum
from airtime.services.base import BaseAirtimeService
from common.enums import ServiceResponseStatus
from integration.sonite.client import SoniteClient
from integration.sonite.dto import VendVtuPayload, SoniteResponse


class SoniteAirtimeService(BaseAirtimeService):
    """Sonite implementation of the airtime service interface."""

    STATUS_MAPPINGS = {
        "00": ServiceResponseStatus.Success,
        "09": ServiceResponseStatus.Pending,
    }

    # Network to product code mapping
    NETWORK_PRODUCT_CODES = {
        AirtimeNetworkEnum.MTN: "MTNVTU",
        AirtimeNetworkEnum.AIRTEL: "AIRTELVTU",
        AirtimeNetworkEnum.GLO: "GLOVTU",
        AirtimeNetworkEnum.ETISALAT: "9MOBILEVTU",
    }

    def __init__(self):
        """Initialize the Sonite airtime client."""
        super().__init__(SoniteClient())

    def _make_airtime_purchase_request(self, params: AirtimePurchaseParams) -> SoniteResponse:
        """Make the actual airtime purchase request to Sonite."""
        payload = self._create_vend_vtu_payload(params)
        _, response = self.client.vend_vtu(payload)
        return response

    def _extract_response_code(self, response: SoniteResponse) -> str:
        """Extract response code from Sonite response."""
        return response.responseCode

    def _extract_provider_message(self, response: SoniteResponse) -> str:
        """Extract provider message from Sonite response."""
        if response.responseCode == "00":
            return "Airtime Purchase Successful"
        return response.responseMessage

    def _create_vend_vtu_payload(self, params: AirtimePurchaseParams) -> VendVtuPayload:
        """Create the VTU payload for Sonite API from parameters."""
        return VendVtuPayload(
            product_code=self._resolve_product_code(params.network),
            amount=params.amount,
            phone_number=params.phone,
            marchant_ref=params.reference,
        )

    def _resolve_product_code(self, network: AirtimeNetworkEnum) -> str:
        """Resolve network enum to Sonite product code."""
        product_code = self.NETWORK_PRODUCT_CODES.get(network)
        if not product_code:
            raise ValueError(f"Unsupported network: {network}")
        return product_code