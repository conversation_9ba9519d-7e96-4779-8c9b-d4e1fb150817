from airtime.dtos import AirtimePurchaseParams
from airtime.services.base import BaseAirtimeService
from common.enums import ServiceResponseStatus
from integration.shago.client import ShagoClient
from integration.shago.dto import AirtimePurchasePayload, AirtimePurchaseResponse


class ShagoAirtimeService(BaseAirtimeService):
    """Shago implementation of the airtime service interface."""

    STATUS_MAPPINGS = {
        "200": ServiceResponseStatus.Success,
        "400": ServiceResponseStatus.Pending,
    }

    def __init__(self):
        """Initialize the Shago airtime client."""
        super().__init__(ShagoClient())

    def _make_airtime_purchase_request(self, params: AirtimePurchaseParams) -> AirtimePurchaseResponse:
        """Make the actual airtime purchase request to Shago."""
        payload = self._create_purchase_payload(params)
        _, response = self.client.purchase_airtime(payload)
        return response

    def _extract_response_code(self, response: AirtimePurchaseResponse) -> str:
        return response.status

    def _extract_provider_message(self, response: AirtimePurchaseResponse) -> str:
        return response.message

    @staticmethod
    def _create_purchase_payload(params: AirtimePurchaseParams) -> AirtimePurchasePayload:
        """Create the purchase payload for Shago API from parameters."""
        return AirtimePurchasePayload(
            phone=params.phone,
            amount=params.amount,
            network=params.network.value,
            request_id=params.reference,
        )