import logging
from abc import ABC
from typing import Dict
from airtime.contracts import AirtimeServiceInterface
from airtime.dtos import AirtimePurchaseParams, AirtimeResponseDetails
from common.constants import GENERIC_EXCEPTION_ERROR_MESSAGE
from common.dtos import ServiceResponseData
from common.enums import ServiceResponseStatus


logger = logging.getLogger(__name__)

class BaseAirtimeService(AirtimeServiceInterface, ABC):
    """Base class for airtime services with common functionality."""

    STATUS_MAPPINGS: Dict[str, ServiceResponseStatus] = {}

    def __init__(self, client):
        self.client = client

    def vend(self, params: AirtimePurchaseParams) -> ServiceResponseData:
        """
        Purchase airtime through the provider.

        Args:
            params: Airtime purchase parameters

        Returns:
            ServiceResponseData with status, message, and airtime details
        """
        airtime_details = self._create_airtime_response_details(params)

        try:
            response = self._make_airtime_purchase_request(params)
            return self._process_airtime_response(response, airtime_details)

        except Exception as e:
            logger.exception(
                "Airtime purchase failed for reference %s: %s",
                params.reference,
                str(e)
            )
            return self._create_error_response(airtime_details)

    @staticmethod
    def _create_airtime_response_details(params: AirtimePurchaseParams) -> AirtimeResponseDetails:
        """Create airtime response details from purchase parameters."""
        return AirtimeResponseDetails(
            network=params.network.value,
            phone=params.phone,
            amount=params.amount
        )

    def _process_airtime_response(self, response, airtime_details: AirtimeResponseDetails) -> ServiceResponseData:
        """Process the response from provider and return appropriate service response."""
        status = self._determine_response_status(response)
        provider_message = self._extract_provider_message(response)

        return ServiceResponseData(
            status=status.value,
            message=provider_message,
            data=airtime_details.to_dict(),
        )

    def _determine_response_status(self, response) -> ServiceResponseStatus:
        """Determine the service response status based on provider response."""
        response_code = self._extract_response_code(response)
        return self.STATUS_MAPPINGS.get(response_code, ServiceResponseStatus.Failed)

    @staticmethod
    def _create_error_response(airtime_details: AirtimeResponseDetails) -> ServiceResponseData:
        """Create a standardized error response for exceptions."""
        return ServiceResponseData(
            status=ServiceResponseStatus.Pending.value,
            message=GENERIC_EXCEPTION_ERROR_MESSAGE,
            data=airtime_details.to_dict(),
        )

    # Abstract methods to be implemented by subclasses
    def _make_airtime_purchase_request(self, params: AirtimePurchaseParams):
        """Make the actual airtime purchase request to provider."""
        raise NotImplementedError("Subclasses must implement this method")

    def _extract_response_code(self, response) -> str:
        """Extract response code from provider response."""
        raise NotImplementedError("Subclasses must implement this method")

    def _extract_provider_message(self, response) -> str:
        """Extract provider message from response."""
        raise NotImplementedError("Subclasses must implement this method")