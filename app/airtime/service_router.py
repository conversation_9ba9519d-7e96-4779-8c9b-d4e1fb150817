from airtime.contracts import AirtimeServiceInterface
from airtime.services.shago import ShagoAirtimeService
from airtime.services.sonite import SoniteAirtimeService
from common.enums import VenderEnum


class AirtimePurchaseServiceRouter:
    """
    Routes airtime purchase requests to the appropriate vendor-specific implementation.

    This class acts as a dynamic service router that delegates airtime-related method calls
    (e.g., purchase) to the correct service implementation (such as Shago or Sonite)
    based on the provided `driver` (vendor). This abstraction allows the business logic to interact
    with a unified interface while enabling easy switching between different airtime service providers.
    """


    @staticmethod
    def resolve(driver: VenderEnum) -> AirtimeServiceInterface:
        """
        Returns the appropriate airtime purchase service based on the driver.

        Returns:
            AirtimeServiceInterface: The airtime purchase service instance.
        """
        if driver == VenderEnum.Shago:
            return ShagoAirtimeService()
        else:
            return SoniteAirtimeService()
