from dataclasses import dataclass
from typing import Optional

from common.enums import ServiceResponseStatus
from common.utils import ToDict


@dataclass
class ServiceResponseData(ToDict):
    status: str
    message: Optional[str] = None
    data: Optional[dict or list] = None
    status_code: Optional[int] = None


    def __post_init__(self):
        if self.status_code is None:
            self.status_code = self._get_status_code_from_status()

    def _get_status_code_from_status(self) -> int:
        status_mapping = {
            ServiceResponseStatus.Pending.value : 200,
            ServiceResponseStatus.Success.value : 200,
            ServiceResponseStatus.Failed.value : 400,
        }
        return status_mapping.get(self.status.lower(), 200)