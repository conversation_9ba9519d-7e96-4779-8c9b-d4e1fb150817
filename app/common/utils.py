from dataclasses import asdict, fields
from datetime import datetime
from typing import Any, Dict, Optional


class ToDict:
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class FieldMapper:
    def __init__(self, **kwargs):
        allowed_keys = {f.name for f in fields(self)}
        for key in allowed_keys:
            if key in kwargs:
                setattr(self, key, kwargs[key])


def parse_gender(gender: Optional[str]) -> Optional[str]:
    if gender is None:
        return None
    gender = gender.lower()
    return {"m": "male", "f": "female"}.get(gender, gender)


def format_date(date_str: Optional[str]) -> Optional[str]:
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, "%Y-%m-%d").strftime("%b %d, %Y")
    except ValueError:
        return None
