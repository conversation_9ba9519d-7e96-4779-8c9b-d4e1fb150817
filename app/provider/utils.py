from django.core.cache import cache
from common.enums import Vender<PERSON>num, ProductEnum, ProviderEnum
from provider.exceptions import ProviderNotFoundException
from provider.models import DefaultProvider

def get_vender(product: ProductEnum, provider: ProviderEnum) -> VenderEnum:
    cache_key = f"default_provider:{product.value}:{provider.value}"
    cached_vender = cache.get(cache_key)

    if cached_vender:
        return VenderEnum(cached_vender)

    default_provider = DefaultProvider.objects.filter(product=product.value, provider=provider.value).first()

    if not default_provider:
        raise ProviderNotFoundException()

    cache.set(cache_key, default_provider.vender, timeout=3600)  # Cache for 1 hour
    return VenderEnum(default_provider.vender)
