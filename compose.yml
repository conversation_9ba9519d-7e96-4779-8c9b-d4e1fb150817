name: "sagecloud-vas-gate"

services:
  db:
    image: postgres:16-alpine
    platform: linux/amd64
    ports:
      - "52432:5432"
    env_file:
      - ./.env
    healthcheck:
      test: [ "CMD", "pg_isready" ]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - db-data:/var/lib/postgresql/data

  api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    image: capitalsage/sagecloud-core
#    command: gunicorn core.wsgi:application --bind 0.0.0.0:52001 --workers 4 --threads 2 --timeout 120
    command: python3 manage.py runserver 0.0.0.0:52001
    volumes:
      - ./app:/app
    ports:
      - "52001:52001"
    env_file:
      - ./.env
    restart: always
    depends_on:
      - db
      - redis

  redis:
    image: redis:7-alpine
    platform: linux/amd64
    ports:
      - "62379:6379"
    volumes:
      - redis-data:/data
    env_file:
      - ./.env

#  redis-commander:
#    image: rediscommander/redis-commander:latest
#    platform: linux/amd64
#    environment:
#      - REDIS_HOSTS=local:redis:6379
#      - HTTP_USER=root
#      - HTTP_PASSWORD=qwerty
#      - REDIS_DB=0
#    ports:
#      - "42081:8081"
#    depends_on:
#      - redis

  dashboard:
    build:
      context: .
      dockerfile: docker/celery/Dockerfile-celery
    command: celery --broker=${CELERY_BROKER_URL} flower --port=5555
    ports:
      - "52555:5555"
    env_file:
      - ./.env
    depends_on:
      - api
      - redis

volumes:
  db-data:
  redis-data:
